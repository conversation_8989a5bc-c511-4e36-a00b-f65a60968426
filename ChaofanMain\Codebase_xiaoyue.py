#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
啸岳处理器 - 五步FFmpeg处理流程
功能标识: xiaoyue_processor
支持：陨星AB、烈锋AB、擎苍AB、啸岳AB
"""

import os
import cv2
import subprocess
import tempfile
import shutil
from pathlib import Path
from PyQt5.QtCore import QThread, pyqtSignal

class XiaoyueProcessor(QThread):
    """啸岳处理器 - 五步FFmpeg处理流程"""

    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)

    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, function_name):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.function_name = function_name
        self.is_cancelled = False
        self.temp_dir = None

        print(f"[啸岳] 处理器初始化完成，功能: {function_name}")

    def get_video_info(self, video_path):
        """获取视频信息（分辨率和时长）"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None, None, None
            
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS) or 30
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps
            
            cap.release()
            return width, height, duration
        except Exception as e:
            print(f"[啸岳] 获取视频信息失败: {e}")
            return None, None, None

    def run(self):
        """主处理流程"""
        try:
            if not self.a_video_list or not self.b_video_list:
                self.process_finished.emit(False, "A视频或B视频列表为空")
                return

            total_videos = len(self.a_video_list)
            success_count = 0

            for i, a_video in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break

                # 选择B视频（循环使用）
                b_video = self.b_video_list[i % len(self.b_video_list)]
                
                # 更新进度
                progress = int((i / total_videos) * 100)
                self.progress_updated.emit(progress, f"处理第{i+1}/{total_videos}个视频...")

                # 处理单个视频对
                if self.process_video_pair(a_video, b_video, i + 1):
                    success_count += 1
                else:
                    print(f"[啸岳] 第{i+1}个视频处理失败")

            # 完成处理
            if success_count > 0:
                self.process_finished.emit(True, f"啸岳处理完成！成功处理 {success_count}/{total_videos} 个视频")
            else:
                self.process_finished.emit(False, "所有视频处理失败")

        except Exception as e:
            print(f"[啸岳] 处理过程异常: {e}")
            self.process_finished.emit(False, f"处理异常: {str(e)}")

    def process_video_pair(self, a_video, b_video, index):
        """处理单个视频对 - 五步FFmpeg流程"""
        try:
            # 获取A视频信息（以A视频为准）
            a_width, a_height, a_duration = self.get_video_info(a_video)
            if not all([a_width, a_height, a_duration]):
                print(f"[啸岳] 无法获取A视频信息: {a_video}")
                return False

            print(f"[啸岳] A视频信息: {a_width}x{a_height}, 时长: {a_duration:.2f}秒")

            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="xiaoyue_")
            
            # 定义临时文件路径
            temp1 = os.path.join(self.temp_dir, "temp1.mp4")
            temp2 = os.path.join(self.temp_dir, "temp2.mp4")
            temp3 = os.path.join(self.temp_dir, "temp3.mp4")
            temp4 = os.path.join(self.temp_dir, "temp4.mp4")
            
            # 生成输出文件名
            a_name = os.path.splitext(os.path.basename(a_video))[0]
            output_filename = f"{a_name}_{self.function_name}.mp4"
            final_output = os.path.join(self.output_dir, output_filename)

            # 第一步：处理A视频主体
            if not self.step1_process_a_main(a_video, temp1, a_duration):
                return False

            # 第二步：处理A视频片段
            if not self.step2_process_a_segment(a_video, temp2):
                return False

            # 第三步：处理B视频
            if not self.step3_process_b_video(b_video, temp3, a_duration):
                return False

            # 第四步：合并A视频片段和B视频
            if not self.step4_concat_videos(temp2, temp3, temp4, a_duration):
                return False

            # 第五步：最终合成
            if not self.step5_final_compose(temp1, temp4, final_output, a_duration):
                return False

            print(f"[啸岳] 视频处理完成: {output_filename}")
            return True

        except Exception as e:
            print(f"[啸岳] 处理视频对失败: {e}")
            return False
        finally:
            # 清理临时文件
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    shutil.rmtree(self.temp_dir)
                except:
                    pass

    def step1_process_a_main(self, a_video, output, duration):
        """第一步：处理A视频主体"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", a_video,
            "-vf", "scale=576:1024:force_original_aspect_ratio=decrease,pad=576:1024:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "faster",
            "-x264-params", "nal-hrd=cbr",
            "-b:v", "15000k", "-profile:v", "high", "-level", "4.0",
            "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain",
            "-c:a", "copy", "-threads", "0",
            output
        ]
        
        print("[啸岳] 执行第一步：处理A视频主体")
        return self.run_ffmpeg_command(cmd, "第一步处理")

    def step2_process_a_segment(self, a_video, output):
        """第二步：处理A视频片段"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", a_video,
            "-ss", "00:00:00", "-t", "0.333",
            "-vf", "scale=576:1024:force_original_aspect_ratio=decrease,pad=576:1024:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "faster",
            "-x264-params", "nal-hrd=cbr",
            "-b:v", "15000k", "-profile:v", "high", "-level", "4.0",
            "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-init_qpP", "0",
            "-quality", "best", "-rc", "cbr", "-threads", "0",
            output
        ]
        
        print("[啸岳] 执行第二步：处理A视频片段")
        return self.run_ffmpeg_command(cmd, "第二步处理")

    def step3_process_b_video(self, b_video, output, duration):
        """第三步：处理B视频"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-stream_loop", "-1",
            "-i", b_video,
            "-t", str(duration),
            "-vf", "scale=576:1024:force_original_aspect_ratio=increase,crop=576:1024:(iw-ow)/2:(ih-oh)/2,setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "faster",
            "-x264-params", "nal-hrd=cbr",
            "-b:v", "3M", "-profile:v", "high", "-level", "4.0",
            "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain",
            "-an", "-threads", "0",
            output
        ]
        
        print("[啸岳] 执行第三步：处理B视频")
        return self.run_ffmpeg_command(cmd, "第三步处理")

    def step4_concat_videos(self, temp2, temp3, output, duration):
        """第四步：合并A视频片段和B视频"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", temp2,
            "-i", temp3,
            "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
            "-c:v", "libx264", "-preset", "faster",
            "-x264-params", "nal-hrd=cbr",
            "-b:v", "3M", "-profile:v", "high", "-level", "4.0",
            "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain", "-threads", "0",
            output
        ]
        
        print("[啸岳] 执行第四步：合并视频")
        return self.run_ffmpeg_command(cmd, "第四步处理")

    def step5_final_compose(self, temp1, temp4, output, duration):
        """第五步：最终合成"""
        cmd = [
            self.ffmpeg_path, "-y",
            "-i", temp1,
            "-i", temp4,
            "-filter_complex", r"[0:v]setsar=1,select=not(mod(n\,2))[a];[1:v]setsar=1,select=mod(n\,2)[b];[a][b]interleave,select=not(eq(n\,0))[v]",
            "-map", "[v]",
            "-c:v", "libx264", "-preset", "faster",
            "-x264-params", "nal-hrd=cbr:vbv-maxrate=15000:vbv-bufsize=15000",
            "-b:v", "15000k", "-profile:v", "high", "-level", "4.2",
            "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain",
            "-vsync", "vfr", "-rc", "constqp", "-qp", "30", "-bf", "0",
            "-video_track_timescale", "1000000",
            "-map", "0:a:0", "-c:a", "copy",
            "-t", str(duration), "-threads", "0",
            output
        ]
        
        print("[啸岳] 执行第五步：最终合成")
        return self.run_ffmpeg_command(cmd, "第五步处理")

    def run_ffmpeg_command(self, cmd, step_name):
        """执行FFmpeg命令"""
        try:
            print(f"[啸岳] {step_name}命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                print(f"[啸岳] {step_name}失败:")
                print(f"[啸岳] 返回码: {result.returncode}")
                if result.stderr:
                    print(f"[啸岳] 错误输出: {result.stderr}")
                return False
            
            print(f"[啸岳] {step_name}成功")
            return True
            
        except subprocess.TimeoutExpired:
            print(f"[啸岳] {step_name}超时")
            return False
        except Exception as e:
            print(f"[啸岳] {step_name}异常: {e}")
            return False

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        print("[啸岳] 处理已取消")
