[2025-07-28T18:00:59.407749 8584 ] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T18:00:59.407815 8584 ] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T18:00:59.407836 8584 ] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T18:00:59.407836 8584 ] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) base_dir = 
[2025-07-28T18:00:59.407836 8584 ] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T18:00:59.407836 8584 ] Config: (default) compiler = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) compiler_check = mtime
[2025-07-28T18:00:59.407836 8584 ] Config: (default) compiler_type = auto
[2025-07-28T18:00:59.407836 8584 ] Config: (default) compression = true
[2025-07-28T18:00:59.407836 8584 ] Config: (default) compression_level = 0
[2025-07-28T18:00:59.407836 8584 ] Config: (default) cpp_extension = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) debug = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) debug_dir = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) debug_level = 2
[2025-07-28T18:00:59.407836 8584 ] Config: (default) depend_mode = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) direct_mode = true
[2025-07-28T18:00:59.407836 8584 ] Config: (default) disable = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) extra_files_to_hash = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) file_clone = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) hard_link = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) hash_dir = true
[2025-07-28T18:00:59.407836 8584 ] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) ignore_options = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) inode_cache = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) keep_comments_cpp = false
[2025-07-28T18:00:59.407836 8584 ] Config: (environment) log_file = E:\MONEYC~1\dist\SHIYON~1.ONE\ccache-33200.txt
[2025-07-28T18:00:59.407836 8584 ] Config: (default) max_files = 0
[2025-07-28T18:00:59.407836 8584 ] Config: (default) max_size = 5.0 GiB
[2025-07-28T18:00:59.407836 8584 ] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T18:00:59.407836 8584 ] Config: (default) namespace = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) path = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) pch_external_checksum = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) prefix_command = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) prefix_command_cpp = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) read_only = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) read_only_direct = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) recache = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) remote_only = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) remote_storage = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) reshare = false
[2025-07-28T18:00:59.407836 8584 ] Config: (default) run_second_cpp = true
[2025-07-28T18:00:59.407836 8584 ] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T18:00:59.407836 8584 ] Config: (default) stats = true
[2025-07-28T18:00:59.407836 8584 ] Config: (default) stats_log = 
[2025-07-28T18:00:59.407836 8584 ] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T18:00:59.407836 8584 ] Config: (default) umask = 
[2025-07-28T18:00:59.407898 8584 ] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=20 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-07-28T18:00:59.410501 8584 ] Hostname: DESKTOP-419NV80
[2025-07-28T18:00:59.410520 8584 ] Working directory: E:\MONEYC~1\dist\SHIYON~1.ONE
[2025-07-28T18:00:59.410538 8584 ] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T18:00:59.410542 8584 ] Compiler type: gcc
[2025-07-28T18:00:59.410724 8584 ] Detected input file: static_src\OnefileBootstrap.c
[2025-07-28T18:00:59.410814 8584 ] Source file: static_src\OnefileBootstrap.c
[2025-07-28T18:00:59.410818 8584 ] Object file: static_src\OnefileBootstrap.o
[2025-07-28T18:00:59.410928 8584 ] Trying direct lookup
[2025-07-28T18:00:59.411077 8584 ] Manifest key: d795ugteehjsuhv8ult9la8so4c9th7pe
[2025-07-28T18:00:59.411347 8584 ] Retrieved d795ugteehjsuhv8ult9la8so4c9th7pe from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/7/95ugteehjsuhv8ult9la8so4c9th7peM)
[2025-07-28T18:00:59.432209 8584 ] Got result key from manifest
[2025-07-28T18:00:59.432243 8584 ] Result key: ec03ukuerabjglahs85qbqsh9066nvd9e
[2025-07-28T18:00:59.433015 8584 ] Retrieved ec03ukuerabjglahs85qbqsh9066nvd9e from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/c/03ukuerabjglahs85qbqsh9066nvd9eR)
[2025-07-28T18:00:59.433602 8584 ] Reading embedded entry #0 .o (612715 bytes)
[2025-07-28T18:00:59.433618 8584 ] Writing to static_src\OnefileBootstrap.o
[2025-07-28T18:00:59.434030 8584 ] Succeeded getting cached result
[2025-07-28T18:00:59.434164 8584 ] Result: direct_cache_hit
[2025-07-28T18:00:59.434168 8584 ] Result: local_storage_hit
[2025-07-28T18:00:59.434171 8584 ] Result: local_storage_read_hit
[2025-07-28T18:00:59.434174 8584 ] Result: local_storage_read_hit
[2025-07-28T18:00:59.434192 8584 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/8/stats.lock
[2025-07-28T18:00:59.434705 8584 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/8/stats.lock
[2025-07-28T18:00:59.435118 8584 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/8/stats.lock
[2025-07-28T18:00:59.435177 8584 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/8/stats.lock
