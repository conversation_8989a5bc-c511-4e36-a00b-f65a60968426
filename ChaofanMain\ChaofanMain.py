import sys
import os
import subprocess
import requests
import json
import hashlib
import platform
import logging
import base64
import psutil
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QLineEdit, QPushButton,
                           QTableWidget, QTabWidget, QCheckBox, QGroupBox,
                           QGridLayout, QSplitter, QHeaderView, QFrame,
                           QFileDialog, QMessageBox, QProgressBar, QTableWidgetItem,
                           QScrollArea)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QSettings
from PyQt5.QtGui import QFont
import cv2
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# API配置
API_NEW_LOGIN = "http://************/api/keysystem/verify_key.php"  # 新登录通道
API_NEW_UNBIND = "http://************/api/keysystem/unbind.php"     # 新解绑通道
SOFTWARE_ID = "UHX6Q9ZN2EXOOI58"  # 超凡软件标识
VERSION = "4.1+"

# 新通道错误码映射
NEW_ERROR_MESSAGES = {
    "-1001": "参数错误",
    "-1002": "卡密已禁用",
    "-1003": "卡密已过期",
    "-1004": "卡密不存在",
    "-1005": "软件不存在或已禁用",
    "-1006": "版本号无效",
    "-1007": "API密钥无效",
    "-1008": "IP被禁止",
    "-1009": "需要更新版本",
    "-1010": "请求过于频繁",
    "-1011": "缺少机器码",
    "-1012": "机器码不匹配",
    "-1097": "黑名单检查失败",
    "-1098": "数据库查询错误",
    "-1099": "系统错误",
    "-1101": "解绑失败，已达到最大解绑次数",
    "-1102": "不允许解绑",
    "-1201": "积分不足",
    "-1301": "功能未授权"
}

def get_machine_code():
    """获取机器码"""
    try:
        # 获取CPU信息
        cpu_info = platform.processor()
        # 获取系统信息
        system_info = platform.system() + platform.release()
        # 获取用户名
        username = os.getenv('USERNAME', 'unknown')

        # 组合信息并生成MD5
        combined = f"{cpu_info}{system_info}{username}"
        machine_code = hashlib.md5(combined.encode()).hexdigest()[:16].upper()
        return machine_code
    except Exception as e:
        print(f"获取机器码失败: {e}")
        return "DEFAULT_MACHINE_CODE"

def detect_packet_capture_software():
    """检测抓包软件"""
    try:
        # 常见抓包软件进程名列表
        packet_capture_processes = [
            'wireshark.exe',
            'tshark.exe',
            'fiddler.exe',
            'charles.exe',
            'burpsuite.exe',
            'burp.exe',
            'mitmproxy.exe',
            'proxyman.exe',
            'httpanalyzer.exe',
            'httpdebugger.exe',
            'httpwatch.exe',
            'networkminer.exe',
            'tcpdump.exe',
            'windump.exe',
            'ettercap.exe',
            'cain.exe',
            'nmap.exe',
            'zenmap.exe',
            'aircrack-ng.exe',
            'kismet.exe',
            'netstumbler.exe',
            'inssider.exe',
            'acrylic.exe',
            'omnipeek.exe',
            'observer.exe',
            'sniffer.exe',
            'packetcapture.exe',
            'networksniffer.exe',
            'smartsniff.exe',
            'rawcap.exe',
            'netresview.exe',
            'networx.exe',
            'glasswire.exe',
            'netlimiter.exe',
            'networkminer.exe'
        ]

        # 获取所有运行的进程
        running_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                process_name = proc.info['name'].lower()
                running_processes.append(process_name)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        # 检查是否有抓包软件在运行
        detected_software = []
        for capture_process in packet_capture_processes:
            if capture_process.lower() in running_processes:
                detected_software.append(capture_process)

        if detected_software:
            print(f"检测到抓包软件: {', '.join(detected_software)}")
            return True, detected_software
        else:
            print("未检测到抓包软件")
            return False, []

    except Exception as e:
        print(f"检测抓包软件时出错: {e}")
        # 出错时为了安全起见，假设检测到了抓包软件
        return True, ["检测异常"]

class NetworkThread(QThread):
    """网络请求线程"""
    finished = pyqtSignal(object)  # 完成信号
    error = pyqtSignal(str)        # 错误信号

    def __init__(self, url, data, headers=None):
        super().__init__()
        self.url = url
        self.data = data
        self.headers = headers or {"Content-Type": "application/json;charset=UTF-8"}
        self._is_running = True

    def run(self):
        try:
            if not self._is_running:
                return

            response = requests.post(
                self.url,
                json=self.data,
                headers=self.headers,
                timeout=(5, 10),
                verify=False
            )

            if not self._is_running:
                return

            self.finished.emit(response)

        except Exception as e:
            if self._is_running:
                self.error.emit(str(e))

    def stop(self):
        self._is_running = False

class CustomFenghuoV2Processor(QThread):
    """自定义风火轮二代处理器，修改输出文件名和进度显示"""

    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    video_completed = pyqtSignal(int)  # 视频完成信号

    def __init__(self, ffmpeg_path, a_video_list, image_folder_path, output_dir, config_code, delete_used_b, selected_function, main_window):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.image_folder_path = image_folder_path
        self.output_dir = output_dir
        self.config_code = config_code
        self.delete_used_b = delete_used_b
        self.selected_function = selected_function
        self.main_window = main_window
        self.is_cancelled = False
        self.total_videos = len(a_video_list)
        self.current_video_index = 0

    def run(self):
        """运行处理"""
        try:
            # 直接在这里实现处理逻辑，逐个处理视频
            self.process_all_videos()

        except Exception as e:
            print(f"自定义处理器运行失败: {e}")
            self.process_finished.emit(False, str(e))

    def process_all_videos(self):
        """处理所有视频"""
        try:
            from Codebase_Fenghuo_v2 import FenghuoV2Processor

            # 获取图片文件列表
            image_files = self.get_image_files()
            if not image_files:
                self.process_finished.emit(False, "B素材文件夹中没有找到图片文件")
                return

            print(f"[一键出同框] 开始处理 {self.total_videos} 个视频")
            print(f"[一键出同框] 选择的功能: {self.selected_function}")

            # 逐个处理视频
            for i, a_video in enumerate(self.a_video_list):
                if self.is_cancelled:
                    break

                self.current_video_index = i

                # 选择对应的图片
                image_file = image_files[i % len(image_files)]

                # 处理单个视频
                success = self.process_single_video(a_video, image_file, i)

                if not success:
                    self.process_finished.emit(False, f"处理第 {i+1} 个视频失败")
                    return

                # 更新表格状态
                self.video_completed.emit(i)

                # 进度条回到0%准备下一个视频
                if i < self.total_videos - 1:
                    self.progress_updated.emit(0, f"准备处理下一个视频...")

            print(f"[一键出同框] 所有视频处理完成")
            self.process_finished.emit(True, "")

        except Exception as e:
            print(f"处理所有视频失败: {e}")
            self.process_finished.emit(False, str(e))

    def get_image_files(self):
        """获取图片文件列表"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
        image_files = []

        try:
            for filename in os.listdir(self.image_folder_path):
                file_path = os.path.join(self.image_folder_path, filename)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in image_extensions:
                        image_files.append(file_path)
        except Exception as e:
            print(f"获取图片文件失败: {e}")

        return sorted(image_files)

    def process_single_video(self, a_video, image_file, video_index):
        """处理单个视频"""
        try:
            # 创建临时处理器来处理单个视频
            from Codebase_Fenghuo_v2 import FenghuoV2Processor

            # 创建单视频处理器
            single_processor = FenghuoV2Processor(
                self.ffmpeg_path, [a_video], self.image_folder_path,
                self.output_dir, self.config_code, self.delete_used_b, self.selected_function
            )

            # 连接信号来监控进度
            single_processor.progress_updated.connect(self.on_single_video_progress)
            single_processor.process_finished.connect(self.on_single_video_finished)

            # 启动处理
            single_processor.start()
            single_processor.wait()  # 等待完成

            # 重命名输出文件
            self.rename_single_output_file(a_video)

            return True

        except Exception as e:
            print(f"处理单个视频失败: {e}")
            return False

    def on_single_video_progress(self, value, message):
        """单个视频进度更新"""
        # 显示当前视频的进度
        video_info = f"({self.current_video_index + 1}/{self.total_videos})"
        self.progress_updated.emit(value, f"{message} {video_info}")

    def on_single_video_finished(self, success, error_msg):
        """单个视频处理完成"""
        if success:
            print(f"[一键出同框] 第 {self.current_video_index + 1} 个视频处理完成")
        else:
            print(f"[一键出同框] 第 {self.current_video_index + 1} 个视频处理失败: {error_msg}")

    def rename_single_output_file(self, a_video):
        """重命名单个输出文件（现在文件名已经正确，无需重命名）"""
        try:
            a_basename = os.path.splitext(os.path.basename(a_video))[0]
            expected_filename = f"{a_basename}_{self.selected_function}.mp4"
            expected_path = os.path.join(self.output_dir, expected_filename)

            if os.path.exists(expected_path):
                print(f"[一键出同框] 文件已正确命名: {expected_filename}")
            else:
                print(f"[一键出同框] 警告: 预期文件不存在: {expected_filename}")

        except Exception as e:
            print(f"检查文件命名时出错: {e}")

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True


class TKPreProcessor(QThread):
    """TK技术前置处理器"""
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)

    def __init__(self, ffmpeg_path, video_files, output_dir):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.video_files = video_files
        self.output_dir = output_dir
        self.is_cancelled = False
        self.total_videos = len(video_files)
        self.current_video = 0

    def run(self):
        """开始处理视频"""
        try:
            print(f"[TK前置处理] 开始处理 {self.total_videos} 个视频")

            for i, video_path in enumerate(self.video_files):
                if self.is_cancelled:
                    break

                self.current_video = i + 1
                video_name = os.path.basename(video_path)
                video_name_without_ext = os.path.splitext(video_name)[0]

                # 更新进度
                progress = int((i / self.total_videos) * 100)
                self.progress_updated.emit(progress, f"正在处理: {video_name} ({self.current_video}/{self.total_videos})")

                # 输出文件路径
                output_filename = f"{video_name_without_ext}_1080P_30fps.mp4"
                output_path = os.path.join(self.output_dir, output_filename)

                # 如果输出文件已存在，跳过
                if os.path.exists(output_path):
                    print(f"[TK前置处理] 文件已存在，跳过: {output_filename}")
                    continue

                # 执行FFmpeg转换
                success = self.convert_video(video_path, output_path, video_name)
                if not success:
                    self.process_finished.emit(False, f"处理视频失败: {video_name}")
                    return

            # 处理完成
            self.progress_updated.emit(100, "所有视频处理完成")
            self.process_finished.emit(True, f"成功处理 {self.total_videos} 个视频")

        except Exception as e:
            print(f"[TK前置处理] 处理异常: {e}")
            self.process_finished.emit(False, f"处理异常: {str(e)}")

    def convert_video(self, input_path, output_path, video_name):
        """转换单个视频为1080P 30帧 H264格式"""
        try:
            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-vf", "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2",
                "-r", "30",
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23",
                "-c:a", "aac",
                "-b:a", "128k",
                "-y",
                output_path
            ]

            print(f"[TK前置处理] 执行命令: {' '.join(cmd)}")

            # 执行命令
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            stdout, stderr = process.communicate()

            if process.returncode != 0:
                print(f"[TK前置处理] 转换失败: {stderr}")
                return False
            else:
                print(f"[TK前置处理] 转换成功: {video_name}")
                return True

        except Exception as e:
            print(f"[TK前置处理] 转换异常: {e}")
            return False

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True


class RotationProcessor(QThread):
    """模拟旋转处理器"""
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)

    def __init__(self, ffmpeg_path, video_files, output_dir, rotation_count=8):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.video_files = video_files
        self.output_dir = output_dir
        self.rotation_count = rotation_count
        self.is_cancelled = False
        self.total_videos = len(video_files)
        self.current_video = 0

    def run(self):
        """开始处理视频"""
        try:
            print(f"[模拟旋转] 开始处理 {self.total_videos} 个视频，旋转 {self.rotation_count} 转")

            for i, video_path in enumerate(self.video_files):
                if self.is_cancelled:
                    break

                self.current_video = i + 1
                video_name = os.path.basename(video_path)
                video_name_without_ext = os.path.splitext(video_name)[0]

                # 更新进度
                progress = int((i / self.total_videos) * 100)
                self.progress_updated.emit(progress, f"正在旋转: {video_name} ({self.current_video}/{self.total_videos})")

                # 输出文件路径
                output_filename = f"{video_name_without_ext}_旋转{self.rotation_count}转.mov"
                output_path = os.path.join(self.output_dir, output_filename)

                # 如果输出文件已存在，跳过
                if os.path.exists(output_path):
                    print(f"[模拟旋转] 文件已存在，跳过: {output_filename}")
                    continue

                # 执行旋转处理
                success = self.rotate_video(video_path, output_path, video_name)
                if not success:
                    self.process_finished.emit(False, f"旋转视频失败: {video_name}")
                    return

            # 处理完成
            self.progress_updated.emit(100, "所有视频旋转完成")
            self.process_finished.emit(True, f"成功旋转 {self.total_videos} 个视频")

        except Exception as e:
            print(f"[模拟旋转] 处理异常: {e}")
            self.process_finished.emit(False, f"处理异常: {str(e)}")

    def rotate_video(self, input_path, output_path, video_name):
        """旋转单个视频"""
        try:
            # 获取原视频分辨率
            width, height = self.get_video_resolution(input_path)
            print(f"[模拟旋转] 原视频分辨率: {width}x{height}")

            # 计算旋转角度 (向左旋转，每转360度)
            total_rotation = self.rotation_count * 360

            # 构建FFmpeg命令 - 向左快速旋转，保持原分辨率，30帧，输出MOV格式
            cmd = [
                self.ffmpeg_path,
                "-i", input_path,
                "-vf", f"rotate={total_rotation}*PI/180*t:fillcolor=black:ow={width}:oh={height}",
                "-r", "30",
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23",
                "-c:a", "aac",
                "-b:a", "128k",
                "-f", "mov",
                "-y",
                output_path
            ]

            print(f"[模拟旋转] 执行命令: {' '.join(cmd)}")

            # 执行命令
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            stdout, stderr = process.communicate()

            if process.returncode != 0:
                print(f"[模拟旋转] 旋转失败: {stderr}")
                return False
            else:
                print(f"[模拟旋转] 旋转成功: {video_name}")
                return True

        except Exception as e:
            print(f"[模拟旋转] 旋转异常: {e}")
            return False

    def get_video_resolution(self, video_path):
        """获取视频分辨率"""
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return 1920, 1080  # 默认分辨率

            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()

            return width, height
        except Exception:
            return 1920, 1080  # 默认分辨率

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True


# 应用样式表
STYLE_SHEET = """
QMainWindow, QWidget { background-color: #F0F0F0; }
QLineEdit {
    border: 1px solid #D9D9D9; padding: 2px 4px;
    background-color: white; border-radius: 0px;
}
QLineEdit:focus { border: 1px solid #0078D7; }
QPushButton {
    border: 1px solid #D9D9D9; background-color: #F5F5F5;
    padding: 2px 8px; border-radius: 0px;
}
QPushButton:hover { background-color: #E5E5E5; }
QPushButton:pressed { background-color: #D9D9D9; }
QPushButton#processButton {
    border: 2px solid #0078D7; background-color: #F5F5F5;
    color: black; font-weight: normal;
}
QPushButton#processButton:hover { background-color: #E5F3FF; }
QPushButton#processButton:pressed { background-color: #CCE8FF; }
QCheckBox { spacing: 4px; }
QCheckBox::indicator {
    width: 12px; height: 12px; border: 1px solid #D9D9D9;
    background-color: white; border-radius: 0px;
}
QCheckBox::indicator:checked {
    background-color: #0078D7; border: 1px solid #0078D7;
}
QGroupBox {
    font-weight: normal; border: 1px solid #D9D9D9;
    border-radius: 0px; margin-top: 8px; padding-top: 4px;
    background-color: #F0F0F0;
}
QGroupBox::title {
    subcontrol-origin: margin; left: 8px;
    padding: 0 4px 0 4px; background-color: #F0F0F0;
}
QTableWidget {
    border: 1px solid #D9D9D9; gridline-color: #D9D9D9;
    background-color: white; selection-background-color: #E5F3FF;
    show-decoration-selected: 1;
}
QTableWidget::item {
    border-right: 1px solid #D9D9D9;
    border-bottom: 1px solid #D9D9D9; padding: 2px;
}
QTableWidget::item:selected {
    background-color: #E5F3FF;
    color: black;
}
QHeaderView::section {
    background-color: #F5F5F5; border: 1px solid #D9D9D9;
    padding: 2px 4px; border-radius: 0px;
}
QTabWidget::pane {
    border: 1px solid #D9D9D9; border-radius: 0px;
    background-color: #F0F0F0;
}
QTabWidget QWidget { background-color: #F0F0F0; }
QTabBar::tab {
    background-color: #F5F5F5; border: 1px solid #D9D9D9;
    padding: 4px 12px; margin-right: 2px; border-radius: 0px;
}
QTabBar::tab:selected {
    background-color: #F0F0F0; border-bottom: 1px solid #F0F0F0;
}
QTabBar::tab:hover { background-color: #E5E5E5; }
QFrame#leftPanel {
    background-color: #F0F0F0; border: 1px solid #A0A0A0;
    border-radius: 2px;
}
"""

class ChaofanMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("好想要 - v4.4")
        self.setGeometry(100, 100, 800, 680)
        self.setMinimumSize(1000, 580)
        self.setFont(QFont("Microsoft YaHei", 9))
        self.setStyleSheet(STYLE_SHEET)

        # 初始化变量
        self.ffmpeg_path = None
        self.mkvmerge_path = None
        self.a_video_folder = None
        self.b_material_path = None
        self.output_dir = None
        self.video_list = []

        # 登录相关变量
        self.is_logged_in = False
        self.card_key = ""
        self.expiry_time = ""
        self.aes_key = ""
        self.aes_iv = ""
        self.login_thread = None
        self.unbind_thread = None
        self.aes_key_thread = None

        # 处理相关变量
        self.is_processing = False
        self.worker = None
        self.decrypted_code = None

        # 检查并初始化bin文件夹
        self.auto_detect_ffmpeg()

        self._init_ui()

        # 检查登录状态（在UI初始化后）
        self.check_login_status()

    def _init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)

        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        splitter.addWidget(self._create_left_panel())
        splitter.addWidget(self._create_right_panel())
        splitter.setSizes([460, 340])
        
    def _create_left_panel(self):
        """创建左侧面板"""
        left_widget = QFrame()
        left_widget.setObjectName("leftPanel")
        layout = QVBoxLayout(left_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # 视频选择区
        layout.addWidget(self._create_video_group())

        # 视频列表
        self.video_table = self._create_video_table()
        layout.addWidget(self.video_table)

        # 状态区域
        layout.addLayout(self._create_status_area())

        return left_widget

    def _create_video_group(self):
        """创建视频选择组"""
        group = QGroupBox("视频选择区")
        layout = QVBoxLayout(group)
        layout.setSpacing(8)

        # 创建路径输入行
        paths = [
            ("A素材路径:", "a_video_path", self.select_a_video_folder),
            ("B素材路径:", "b_video_path", self.select_b_material_path),
            ("输出路径:", "output_path", self.select_output_dir)
        ]
        for label_text, attr_name, callback in paths:
            row_layout = QHBoxLayout()
            row_layout.addWidget(QLabel(label_text))

            line_edit = QLineEdit()
            line_edit.setReadOnly(True)
            setattr(self, attr_name, line_edit)
            row_layout.addWidget(line_edit)

            btn = QPushButton("浏览")
            btn.setFixedSize(48, 24)
            btn.clicked.connect(callback)
            row_layout.addWidget(btn)

            layout.addLayout(row_layout)

        return group

    def _create_video_table(self):
        """创建视频表格"""
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["序号", "视频名称", "视频信息", "状态"])
        table.setShowGrid(True)

        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        table.setColumnWidth(0, 50)
        table.setColumnWidth(3, 80)

        return table

    def _create_status_area(self):
        """创建状态区域"""
        layout = QVBoxLayout()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat('%p%')
        self.progress_bar.setStyleSheet('''
            QProgressBar {
                background-color: #F0F0F0;
                border: 1px solid #A0A0A0;
                border-radius: 0px;
                text-align: center;
                color: black;
                font-weight: normal;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #0078D7;
                border-radius: 0px;
            }
        ''')
        layout.addWidget(self.progress_bar)

        return layout
    
    def _create_right_panel(self):
        """创建右侧面板"""
        tab_widget = QTabWidget()

        tab_widget.addTab(self._create_func_tab(), "功能选择")

        # 即将上线标签页
        for tab_name in ["新功能区", "视频制作", "使用教程"]:
            tab_widget.addTab(self._create_coming_soon_tab(tab_name), tab_name)

        tab_widget.addTab(self._create_activation_tab(), "软件激活")

        # 默认显示软件激活选项卡
        tab_widget.setCurrentIndex(4)  # 软件激活选项卡的索引

        return tab_widget
    
    def _create_func_tab(self):
        """创建功能选择标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # 添加各功能组
        groups = [
            ("AB融合", [
                "超繁AB", "幻躁AB", "隐身AB", "百万AB",
                "挂链AB", "灰头AB", "小丑AB", "大凡AB",
                "闪帕AB", "无闪AB", "翻牌AB", "成功AB",
                "慢爆AB", "速爆AB", "赤兔AB", "面具2.9",
                "梨汤AB", "超凡AB", "生而AB", "不凡AB",
                "安琪AB", "跺踩AB", "马刀AB", "子牙AB",
                "赛博AB", "深渊AB", "起源AB", "合天下",
                "陨星AB", "烈锋AB", "擎苍AB", "啸岳AB"
            ], 4),
            ("Tiktok", ["爆闪AB", "零式AB", "未来引擎AB"], 3),
            ("小红薯", ["红薯", "紫薯", "粉薯", "马铃薯"], 4),
            ("新AB融合", ["新慢爆", "新翻牌", "新闪帧", "新梨汤", "咪咪", "虾条", "玉溪", "利群"], 4),
            ("一键出同框", ["海盗船", "摩天轮", "过山车", "大摆锤"], 4),
            ("时长选择", ["超凡6秒", "超凡7秒", "爆改6秒", "爆改8秒"], 4)
        ]

        for title, options, cols in groups:
            group = QGroupBox(title)

            if cols == 1:
                group_layout = QVBoxLayout(group)
                for option in options:
                    group_layout.addWidget(QCheckBox(option))
            else:
                group_layout = QGridLayout(group)
                group_layout.setSpacing(8)
                for i, option in enumerate(options):
                    row, col = i // cols, i % cols
                    group_layout.addWidget(QCheckBox(option), row, col)

            layout.addWidget(group)

        # 添加弹性空间和处理按钮
        layout.addStretch()

        self.process_btn = QPushButton("确定处理")
        self.process_btn.setObjectName("processButton")
        self.process_btn.setFixedSize(96, 28)
        self.process_btn.clicked.connect(self.start_processing)
        layout.addWidget(self.process_btn, alignment=Qt.AlignRight)

        return widget
    
    def _create_coming_soon_tab(self, tab_name=None):
        """创建"即将上线"标签页或使用教程"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        if tab_name == "使用教程":
            # 创建使用教程内容
            return self._create_tutorial_tab()
        elif tab_name == "新功能区":
            # 创建新功能区界面
            return self._create_new_function_tab()
        elif tab_name == "视频制作":
            # 创建视频制作界面
            return self._create_video_production_tab()
        else:
            # 创建即将上线标签页
            layout.setAlignment(Qt.AlignCenter)
            label = QLabel("待更新...")
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("font-size: 12px; color: black;")
            layout.addWidget(label)
            return widget

    def _create_new_function_tab(self):
        """创建新功能区标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # 一键出同框区域
        tongkuang_group = QGroupBox("一键出同框")
        tongkuang_layout = QGridLayout(tongkuang_group)
        tongkuang_layout.setSpacing(8)

        # 十二个新功能选项
        new_functions = ["狂鲨", "风暴", "野火", "飞翼", "通灵", "传火", "充能", "咒文", "比比东", "新唐门", "武魂殿", "传灵塔"]

        for i, func_name in enumerate(new_functions):
            row, col = i // 4, i % 4
            tongkuang_layout.addWidget(QCheckBox(func_name), row, col)

        layout.addWidget(tongkuang_group)

        # 添加弹性空间和处理按钮
        layout.addStretch()

        self.new_process_btn = QPushButton("确定处理")
        self.new_process_btn.setObjectName("processButton")
        self.new_process_btn.setFixedSize(96, 28)
        self.new_process_btn.clicked.connect(self.start_new_processing)
        layout.addWidget(self.new_process_btn, alignment=Qt.AlignRight)

        return widget

    def _create_video_production_tab(self):
        """创建视频制作标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # TK技术前置处理区域
        tk_group = QGroupBox("TK技术前置处理（调用剪映导出）")
        tk_group.setObjectName("tk_group")
        tk_layout = QVBoxLayout(tk_group)
        tk_layout.setSpacing(15)

        # 文件夹选择区域
        folder_layout = QVBoxLayout()
        folder_layout.setSpacing(10)

        # 待处理文件夹选择
        input_layout = QHBoxLayout()
        input_label = QLabel("待处理文件夹:")
        input_label.setFixedWidth(100)
        self.tk_input_path = QLineEdit()
        self.tk_input_path.setPlaceholderText("请选择包含视频文件的文件夹")
        self.tk_input_path.setReadOnly(True)
        self.tk_input_btn = QPushButton("选择文件夹")
        self.tk_input_btn.setFixedWidth(100)
        self.tk_input_btn.clicked.connect(self.select_tk_input_folder)

        input_layout.addWidget(input_label)
        input_layout.addWidget(self.tk_input_path)
        input_layout.addWidget(self.tk_input_btn)

        # 输出文件夹选择
        output_layout = QHBoxLayout()
        output_label = QLabel("输出文件夹:")
        output_label.setFixedWidth(100)
        self.tk_output_path = QLineEdit()
        self.tk_output_path.setPlaceholderText("请选择输出文件夹")
        self.tk_output_path.setReadOnly(True)
        self.tk_output_btn = QPushButton("选择文件夹")
        self.tk_output_btn.setFixedWidth(100)
        self.tk_output_btn.clicked.connect(self.select_tk_output_folder)

        output_layout.addWidget(output_label)
        output_layout.addWidget(self.tk_output_path)
        output_layout.addWidget(self.tk_output_btn)

        folder_layout.addLayout(input_layout)
        folder_layout.addLayout(output_layout)
        tk_layout.addLayout(folder_layout)

        # 处理设置说明
        settings_label = QLabel("处理设置：将所有视频转换为 1080P 30帧 H264 格式")
        settings_label.setStyleSheet("color: #666; font-size: 12px; margin: 10px 0;")
        tk_layout.addWidget(settings_label)

        # 模拟旋转设置区域
        rotation_settings_layout = QHBoxLayout()
        rotation_settings_layout.setSpacing(10)

        rotation_label = QLabel("旋转参数:")
        rotation_label.setFixedWidth(70)

        self.rotation_count_input = QLineEdit()
        self.rotation_count_input.setPlaceholderText("8")
        self.rotation_count_input.setText("8")
        self.rotation_count_input.setFixedWidth(60)

        rotation_unit_label = QLabel("转")
        rotation_unit_label.setFixedWidth(20)

        rotation_desc_label = QLabel("(向左快速旋转，模拟手机摄像头旋转)")
        rotation_desc_label.setStyleSheet("color: #666; font-size: 11px;")

        rotation_settings_layout.addWidget(rotation_label)
        rotation_settings_layout.addWidget(self.rotation_count_input)
        rotation_settings_layout.addWidget(rotation_unit_label)
        rotation_settings_layout.addWidget(rotation_desc_label)
        rotation_settings_layout.addStretch()

        tk_layout.addLayout(rotation_settings_layout)

        # 进度显示区域
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(5)

        self.tk_progress_label = QLabel("等待开始处理...")
        self.tk_progress_label.setStyleSheet("color: #333; font-size: 12px;")

        self.tk_progress_bar = QProgressBar()
        self.tk_progress_bar.setRange(0, 100)
        self.tk_progress_bar.setValue(0)
        self.tk_progress_bar.setTextVisible(True)

        progress_layout.addWidget(self.tk_progress_label)
        progress_layout.addWidget(self.tk_progress_bar)
        tk_layout.addLayout(progress_layout)

        # 处理按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.tk_process_btn = QPushButton("剪映处理")
        self.tk_process_btn.setObjectName("tk_process_btn")
        self.tk_process_btn.setFixedSize(120, 35)
        self.tk_process_btn.clicked.connect(self.start_tk_processing)

        self.tk_rotation_btn = QPushButton("模拟旋转")
        self.tk_rotation_btn.setObjectName("tk_rotation_btn")
        self.tk_rotation_btn.setFixedSize(120, 35)
        self.tk_rotation_btn.clicked.connect(self.start_tk_rotation_processing)

        button_layout.addWidget(self.tk_process_btn)
        button_layout.addWidget(self.tk_rotation_btn)
        tk_layout.addLayout(button_layout)

        layout.addWidget(tk_group)
        layout.addStretch()

        # 设置样式
        widget.setStyleSheet("""
            #tk_group {
                border: 2px solid #ddd;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin-top: 10px;
                padding-top: 10px;
            }
            #tk_group::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            #tk_process_btn {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            #tk_process_btn:hover {
                background-color: #45a049;
            }
            #tk_process_btn:pressed {
                background-color: #3d8b40;
            }
            #tk_process_btn:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            #tk_rotation_btn {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            #tk_rotation_btn:hover {
                background-color: #1976D2;
            }
            #tk_rotation_btn:pressed {
                background-color: #0D47A1;
            }
            #tk_rotation_btn:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        return widget

    def _create_tutorial_tab(self):
        """创建使用教程标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建内容widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)

        # 教程内容
        tutorial_text = """════════════《功能使用教程》═══════════
✨ 好想要v4.4版本更新通道：
---------------------------------------------------------------           
✔ 新增通道（均为最新升级版）： 
 陨星AB:主打使用-可过可跑-独家自研
 烈锋AB:主打使用-可过可跑-独家自研
 擎苍AB:主打使用-可过可跑-独家自研
 啸岳AB:主打使用-可过可跑-独家自研
 *这四个功能为新增升级版，某凡所用的旧版已不可过审，支持正版“好想要”持续更新。

 新功能区：

 新慢爆：主打使用-可过可跑-独家自研（新增）
 新翻牌：主打使用-可过可跑-独家自研（新增）
 新闪帧：主打使用-可过可跑-独家自研（新增）
 新梨汤：主打使用-可过可跑-独家自研（新增）

 KS 一键出同框：
 
 狂鲨：可过可跑-条条出框-独家自研（新增）
 风暴：可过可跑-条条出框-独家自研（新增）
 野火：可过可跑-条条出框-独家自研（新增）
 飞翼：可过可跑-条条出框-独家自研（新增） 

--------------------------------------------------------------- 
 
 历史版本功能：
 新慢爆-主打使用-可过可跑-独家自研  
 新翻牌-主打使用-可过可跑-独家自研  
 新闪帧-主打使用-可过可跑-独家自研  
 新梨汤-主打使用-可过可跑-独家自研  
 咪咪-主打使用-可过可跑-独家自研  
 虾条-主打使用-可过可跑-独家自研  
 玉溪-主打使用-可过可跑-独家自研  
 利群-主打使用-可过可跑-独家自研        
 狂鲨-主打使用-可过可跑-独家自研
 风暴-主打使用-可过可跑-独家自研
 野火-主打使用-可过可跑-独家自研
 飞翼-主打使用-可过可跑-独家自研
 比比东-主打使用-可过可跑-独家自研
 新唐门-主打使用-可过可跑-独家自研
 武魂殿-主打使用-可过可跑-独家自研
 传灵塔-主打使用-可过可跑-独家自研

 超繁AB:正常使用
 幻影AB:正常使用
 隐身AB:正常使用
 百万AB:仅限100w以上账号使用-支持挂各种链接（好用）
 挂链AB:正常使用-可过可跑-独家自研
 灰头AB:正常使用-可过可跑-独家自研
 小凡AB:正常使用-可过可跑-独家自研
 大凡AB:主打使用-可过可跑-独家自研
 闪帧AB:主打使用-可过可跑-独家自研
 翻牌AB:主打使用-可过可跑-独家自研
 慢爆AB:主打使用-起推慢-容易 爆       
 速爆AB:主打使用-可过可跑-独家自研
 赤兔AB:主打使用-可过可跑-算法出于（赤兔ab）
 面具AB:主打使用-可过可跑-修复掉画面
 梨汤AB:主打使用-可过可跑-算法出于（疾风-小吊梨汤）
 超凡ABc
 生而AB:主打使用-可过可跑-独家自研
 不凡AB:主打使用-可过可跑-独家自研
 安琪AB:主打使用-可过可跑-算法出于（兰陵王-至尊安琪拉）
 马可AB:主打使用-可过可跑-算法出于（兰陵王-马可波罗）
 璐璐AB:主打使用-可过可跑-算法出于（兰陵王-娜可璐璐）
 子牙AB:主打使用-可过可跑-算法出于（兰陵王-封神姜子牙）
 赛博AB:主打使用-可过可跑-算法出于（面具-赛博ab）
 深渊AB:主打使用-可过可跑-算法出于（面具-深渊ab）
 起源AB:主打使用-可过可跑-算法出于（面具-起源ab）
 和天下 :主打使用-可过可跑-算法出于（合天下-市面割的599）
--------------------------------------------------------------- 
Tiktok

 爆闪AB:主打使用-可过可跑-独家自研
--------------------------------------------------------------- 
小红薯

 红薯：主打使用-可过可跑-独家自研
 紫薯：主打使用-可过可跑-独家自研
 粉薯：主打使用-可过可跑-独家自研
 马薯：主打使用-可过可跑-独家自研
--------------------------------------------------------------- 
KS 一键出同框

 海盗船:B视频不支持图片-条条出框-算法出于（快剪王）
 大摆锤:B视频支持视频-条条出框-算法出于（视频融合ks定制）
 过山车:B视频支持图片视频都-条条出框-算法出于（鬼手）
 摩天轮:B视频支持图片视频都-条条出框-算法出于（摩天轮）
--------------------------------------------------------------- 
⚠ 注意事项：

1、以上功能均不同算法，优先使用最新通道！

2、B素材选用实拍素材，流量跟B视频选用素材关系很大！

3、会出现长视频掉画面，A视频跟B视频使用相同的帧率

4、启动程序时选用管理员模式运行！ """

        # 创建文本标签
        text_label = QLabel(tutorial_text)
        text_label.setWordWrap(True)
        text_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        text_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
                font-size: 11px;
                line-height: 1.4;
                color: #333333;
                background-color: #f8f9fa;
                padding: 15px;
                border: 1px solid #e9ecef;
                border-radius: 5px;
            }
        """)
        text_label.setTextInteractionFlags(Qt.TextSelectableByMouse)  # 允许选择文本但不可编辑

        content_layout.addWidget(text_label)
        content_layout.addStretch()  # 添加弹性空间

        # 设置滚动区域的内容
        scroll_area.setWidget(content_widget)

        # 添加到主布局
        layout.addWidget(scroll_area)

        return widget

    def _create_activation_tab(self):
        """创建软件激活标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)

        layout.addStretch(1)

        # 卡号输入区域
        card_layout = QHBoxLayout()
        self.card_input = QLineEdit()
        self.card_input.setMinimumWidth(250)
        card_layout.addWidget(QLabel("卡号："))
        card_layout.addWidget(self.card_input)
        card_layout.addStretch(1)
        layout.addLayout(card_layout)

        layout.addSpacing(20)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)

        # 登录按钮
        self.login_btn = QPushButton("登陆")
        self.login_btn.setFixedSize(80, 28)
        self.login_btn.clicked.connect(self.login)
        button_layout.addWidget(self.login_btn)
        button_layout.addSpacing(20)

        # 解绑按钮
        self.unbind_btn = QPushButton("解绑")
        self.unbind_btn.setFixedSize(80, 28)
        self.unbind_btn.clicked.connect(self.unbind)
        button_layout.addWidget(self.unbind_btn)

        button_layout.addStretch(1)
        layout.addLayout(button_layout)

        layout.addSpacing(20)

        # 状态提示标签
        self.login_status_label = QLabel("请输入卡密进行登录")
        self.login_status_label.setAlignment(Qt.AlignCenter)
        self.login_status_label.setStyleSheet("color: black; font-size: 12px;")
        layout.addWidget(self.login_status_label)

        layout.addStretch(2)

        return widget

    def auto_detect_ffmpeg(self):
        """自动检测bin目录下的关键组件"""
        # 获取程序所在目录
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        print(f"程序所在目录: {exe_dir}")

        # 检查bin文件夹是否存在
        bin_dir = os.path.join(exe_dir, "bin")
        if not os.path.exists(bin_dir) or not os.path.isdir(bin_dir):
            print("未找到bin文件夹")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)

        # 检查bin文件夹中的ffmpeg.exe是否存在
        ffmpeg_exe = os.path.join(bin_dir, "ffmpeg.exe")
        if not os.path.exists(ffmpeg_exe) or not os.path.isfile(ffmpeg_exe):
            print("未找到ffmpeg.exe")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)

        # 检查bin文件夹中的mkvmerge.exe是否存在
        mkvmerge_exe = os.path.join(bin_dir, "mkvmerge.exe")
        if not os.path.exists(mkvmerge_exe) or not os.path.isfile(mkvmerge_exe):
            print("未找到mkvmerge.exe")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)

        # 检查其他关键文件
        required_files = ["ffprobe.exe", "avcodec-62.dll", "avfilter-11.dll", "avformat-62.dll", "avutil-60.dll", "swresample-6.dll", "swscale-9.dll"]
        missing_files = []

        for file in required_files:
            file_path = os.path.join(bin_dir, file)
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                print(f"未找到关键文件: {file}")
                missing_files.append(file)

        if missing_files:
            print(f"缺少关键文件: {', '.join(missing_files)}")
            QMessageBox.critical(self, "错误", "关键组件丢失，请尝试重新安装！")
            sys.exit(1)

        # 设置ffmpeg和mkvmerge路径
        self.ffmpeg_path = ffmpeg_exe
        self.mkvmerge_path = mkvmerge_exe
        print(f"自动检测到ffmpeg路径: {self.ffmpeg_path}")
        print(f"自动检测到mkvmerge路径: {self.mkvmerge_path}")

        # 验证ffmpeg是否可用
        try:
            result = subprocess.run(
                [ffmpeg_exe, "-version"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            if result.returncode == 0:
                print("ffmpeg验证成功")
            else:
                print(f"ffmpeg验证失败: {result.stderr}")
                QMessageBox.critical(self, "错误", "关键组件验证失败，请尝试重新安装！")
                sys.exit(1)
        except Exception as e:
            print(f"ffmpeg验证异常: {e}")
            QMessageBox.critical(self, "错误", "关键组件验证失败，请尝试重新安装！")
            sys.exit(1)

        # 验证mkvmerge是否可用
        try:
            result = subprocess.run(
                [mkvmerge_exe, "--version"],
                stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                text=True, encoding="utf-8", errors="ignore",
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            if result.returncode == 0:
                print("mkvmerge验证成功")
            else:
                print(f"mkvmerge验证失败: {result.stderr}")
                QMessageBox.critical(self, "错误", "MKV工具集验证失败，请尝试重新安装！")
                sys.exit(1)
        except Exception as e:
            print(f"mkvmerge验证异常: {e}")
            QMessageBox.critical(self, "错误", "MKV工具集验证失败，请尝试重新安装！")
            sys.exit(1)

    def select_a_video_folder(self):
        """选择A素材文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择A素材文件夹")
        if folder_path:
            self.a_video_folder = folder_path
            self.a_video_path.setText(folder_path)

            # 读取文件夹中的视频文件
            self.video_list = self.get_video_files_from_folder(folder_path)
            self.update_video_table()

            # 设置默认输出路径为A视频所在文件夹
            if not self.output_dir:
                self.output_dir = folder_path
                self.output_path.setText(folder_path)

    def select_b_material_path(self):
        """选择B素材路径（可能是图片或视频）"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择B素材文件夹")
        if folder_path:
            self.b_material_path = folder_path
            self.b_video_path.setText(folder_path)

            # 检查文件夹内容
            image_count = self.count_image_files(folder_path)
            video_count = len(self.get_video_files_from_folder(folder_path))

    def select_output_dir(self):
        """选择输出路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_path.setText(dir_path)

    def get_video_files_from_folder(self, folder_path):
        """从文件夹获取视频文件列表"""
        if not folder_path or not os.path.exists(folder_path):
            return []

        # 支持的视频格式
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.ts', '.mts', '.m2ts'}
        video_files = []

        try:
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in video_extensions:
                        video_files.append(file_path)
        except Exception as e:
            print(f"读取文件夹失败: {e}")

        return sorted(video_files)

    def count_image_files(self, folder_path):
        """统计文件夹中的图片文件数量"""
        if not folder_path or not os.path.exists(folder_path):
            return 0

        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'}
        count = 0

        try:
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(filename.lower())
                    if ext in image_extensions:
                        count += 1
        except Exception as e:
            print(f"统计图片文件失败: {e}")

        return count

    def get_video_duration(self, file_path):
        """获取视频时长"""
        try:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                return "00:00:00"
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            duration_seconds = total_frames / fps if fps > 0 else 0
            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)
            cap.release()
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except Exception:
            return "00:00:00"

    def get_video_resolution(self, file_path):
        """获取视频分辨率"""
        try:
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                return "1920*1080"
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()
            return f"{width}*{height}"
        except Exception:
            return "1920*1080"

    def update_video_table(self):
        """更新视频表格"""
        self.video_table.setRowCount(len(self.video_list))

        for i, video_path in enumerate(self.video_list):
            # 序号
            self.video_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

            # 视频名称
            video_name = os.path.basename(video_path)
            self.video_table.setItem(i, 1, QTableWidgetItem(video_name))

            # 视频信息（时长 / 分辨率）
            duration = self.get_video_duration(video_path)
            resolution = self.get_video_resolution(video_path)
            video_info = f"{duration} / {resolution}"
            self.video_table.setItem(i, 2, QTableWidgetItem(video_info))

            # 状态
            self.video_table.setItem(i, 3, QTableWidgetItem("待处理"))

    def select_tk_input_folder(self):
        """选择TK处理输入文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择待处理文件夹")
        if folder_path:
            self.tk_input_path.setText(folder_path)
            # 统计视频文件数量
            video_files = self.get_video_files_from_folder(folder_path)
            if video_files:
                self.tk_progress_label.setText(f"找到 {len(video_files)} 个视频文件，等待开始处理...")
            else:
                self.tk_progress_label.setText("未找到视频文件，请选择包含视频的文件夹")
                QMessageBox.warning(self, "警告", "选择的文件夹中没有找到视频文件")

    def select_tk_output_folder(self):
        """选择TK处理输出文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder_path:
            self.tk_output_path.setText(folder_path)

    def start_tk_processing(self):
        """开始TK技术前置处理"""
        try:
            # 检查登录状态
            if not hasattr(self, 'card_key') or not self.card_key:
                QMessageBox.warning(self, "提示", "请先登录后再使用此功能")
                return

            # 检查输入文件夹
            input_folder = self.tk_input_path.text().strip()
            if not input_folder:
                QMessageBox.warning(self, "错误", "请先选择待处理文件夹")
                return

            if not os.path.exists(input_folder):
                QMessageBox.warning(self, "错误", "待处理文件夹不存在")
                return

            # 检查输出文件夹
            output_folder = self.tk_output_path.text().strip()
            if not output_folder:
                QMessageBox.warning(self, "错误", "请先选择输出文件夹")
                return

            if not os.path.exists(output_folder):
                try:
                    os.makedirs(output_folder)
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"无法创建输出文件夹: {str(e)}")
                    return

            # 获取视频文件列表
            video_files = self.get_video_files_from_folder(input_folder)
            if not video_files:
                QMessageBox.warning(self, "错误", "待处理文件夹中没有找到视频文件")
                return

            # 检查FFmpeg
            if not hasattr(self, 'ffmpeg_path') or not self.ffmpeg_path:
                QMessageBox.warning(self, "错误", "未找到FFmpeg，请检查软件配置")
                return

            # 开始处理
            self.tk_process_btn.setEnabled(False)
            self.tk_process_btn.setText("处理中...")
            self.tk_progress_bar.setValue(0)
            self.tk_progress_label.setText("正在初始化处理...")

            # 创建TK处理器
            self.tk_worker = TKPreProcessor(
                self.ffmpeg_path, video_files, output_folder
            )

            # 连接信号
            self.tk_worker.progress_updated.connect(self.update_tk_progress)
            self.tk_worker.process_finished.connect(self.on_tk_process_finished)

            # 启动处理
            self.tk_worker.start()

        except Exception as e:
            print(f"启动TK处理失败: {e}")
            QMessageBox.critical(self, "错误", f"启动处理失败: {str(e)}")
            self.reset_tk_processing_state()

    def update_tk_progress(self, value, message):
        """更新TK处理进度"""
        self.tk_progress_bar.setValue(value)
        self.tk_progress_label.setText(message)

    def on_tk_process_finished(self, success, message):
        """TK处理完成回调"""
        self.reset_tk_processing_state()

        if success:
            QMessageBox.information(self, "完成", "TK技术前置处理完成！")
            self.tk_progress_label.setText("处理完成")
        else:
            QMessageBox.critical(self, "错误", f"处理失败: {message}")
            self.tk_progress_label.setText(f"处理失败: {message}")

    def reset_tk_processing_state(self):
        """重置TK处理状态"""
        self.tk_process_btn.setEnabled(True)
        self.tk_process_btn.setText("剪映处理")
        self.tk_rotation_btn.setEnabled(True)
        self.tk_rotation_btn.setText("模拟旋转")

    def start_tk_rotation_processing(self):
        """开始TK模拟旋转处理"""
        try:
            # 检查登录状态
            if not hasattr(self, 'card_key') or not self.card_key:
                QMessageBox.warning(self, "提示", "请先登录后再使用此功能")
                return

            # 检查输入文件夹
            input_folder = self.tk_input_path.text().strip()
            if not input_folder:
                QMessageBox.warning(self, "错误", "请先选择待处理文件夹")
                return

            if not os.path.exists(input_folder):
                QMessageBox.warning(self, "错误", "待处理文件夹不存在")
                return

            # 检查输出文件夹
            output_folder = self.tk_output_path.text().strip()
            if not output_folder:
                QMessageBox.warning(self, "错误", "请先选择输出文件夹")
                return

            if not os.path.exists(output_folder):
                try:
                    os.makedirs(output_folder)
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"无法创建输出文件夹: {str(e)}")
                    return

            # 获取视频文件列表
            video_files = self.get_video_files_from_folder(input_folder)
            if not video_files:
                QMessageBox.warning(self, "错误", "待处理文件夹中没有找到视频文件")
                return

            # 检查FFmpeg
            if not hasattr(self, 'ffmpeg_path') or not self.ffmpeg_path:
                QMessageBox.warning(self, "错误", "未找到FFmpeg，请检查软件配置")
                return

            # 获取旋转参数
            try:
                rotation_count = int(self.rotation_count_input.text().strip() or "8")
                if rotation_count <= 0:
                    QMessageBox.warning(self, "错误", "旋转参数必须大于0")
                    return
            except ValueError:
                QMessageBox.warning(self, "错误", "旋转参数必须是有效的数字")
                return

            # 开始处理
            self.tk_process_btn.setEnabled(False)
            self.tk_rotation_btn.setEnabled(False)
            self.tk_rotation_btn.setText("旋转中...")
            self.tk_progress_bar.setValue(0)
            self.tk_progress_label.setText("正在初始化旋转处理...")

            # 创建旋转处理器
            self.tk_rotation_worker = RotationProcessor(
                self.ffmpeg_path, video_files, output_folder, rotation_count
            )

            # 连接信号
            self.tk_rotation_worker.progress_updated.connect(self.update_tk_progress)
            self.tk_rotation_worker.process_finished.connect(self.on_tk_rotation_process_finished)

            # 启动处理
            self.tk_rotation_worker.start()

        except Exception as e:
            print(f"启动TK旋转处理失败: {e}")
            QMessageBox.critical(self, "错误", f"启动旋转处理失败: {str(e)}")
            self.reset_tk_processing_state()

    def on_tk_rotation_process_finished(self, success, message):
        """TK旋转处理完成回调"""
        self.reset_tk_processing_state()

        if success:
            QMessageBox.information(self, "完成", "模拟旋转处理完成！")
            self.tk_progress_label.setText("旋转处理完成")
        else:
            QMessageBox.critical(self, "错误", f"旋转处理失败: {message}")
            self.tk_progress_label.setText(f"旋转处理失败: {message}")

    def check_login_status(self):
        """检查登录状态 - 只加载保存的卡密，不自动登录"""
        try:
            settings = QSettings('WanHaoGL', 'ChaofanMain')
            saved_key = settings.value('saved_key', '')
            remember = settings.value('remember_key', False, type=bool)

            if remember and saved_key:
                # 只填入保存的卡密，不自动登录
                self.card_input.setText(saved_key)
                self.login_status_label.setText("请点击登录按钮进行登录")
                self.login_status_label.setStyleSheet("color: black; font-size: 12px;")
                print(f"已加载保存的卡密: {saved_key}")
            else:
                self.login_status_label.setText("请输入卡密进行登录")
                self.login_status_label.setStyleSheet("color: black; font-size: 12px;")
        except Exception as e:
            print(f"检查登录状态失败: {e}")

    def save_login_credentials(self):
        """保存登录凭据 - 只保存卡密"""
        try:
            settings = QSettings('WanHaoGL', 'ChaofanMain')
            settings.setValue('saved_key', self.card_key)
            settings.setValue('remember_key', True)
            # 不保存登录状态相关信息，每次都需要重新登录
            print(f"已保存卡密: {self.card_key}")
        except Exception as e:
            print(f"保存卡密失败: {e}")

    def clear_login_credentials(self):
        """清除登录凭据"""
        try:
            settings = QSettings('WanHaoGL', 'ChaofanMain')
            settings.remove('saved_key')
            settings.setValue('remember_key', False)
            print("已清除保存的卡密")
        except Exception as e:
            print(f"清除卡密失败: {e}")

    def login(self):
        """处理登录"""
        try:
            # 首先检测抓包软件
            print("正在检测抓包软件...")
            has_packet_capture, detected_software = detect_packet_capture_software()

            if has_packet_capture:
                print(f"检测到抓包软件，程序将退出: {detected_software}")
                QMessageBox.critical(self, '出错啦！',
                                   f'连接服务器失败！')
                sys.exit(1)

            key = self.card_input.text().strip()
            if not key:
                QMessageBox.warning(self, '提示', '请输入卡密')
                return

            key_length = len(key)

            # 检查卡密长度
            if key_length < 16 or key_length > 32:
                QMessageBox.critical(self, '登录失败', '卡密不存在')
                return

            # 禁用登录按钮
            self.login_btn.setEnabled(False)
            self.login_btn.setText('登录中...')

            # 更新状态标签
            self.login_status_label.setText("正在登录中...")
            self.login_status_label.setStyleSheet("color: blue; font-size: 12px;")

            # 获取机器码
            machine_code = get_machine_code()

            # 只使用新通道（17-32位卡密）
            if 17 <= key_length <= 32:
                print("使用新通道登录")
                data = {
                    "software_identifier": SOFTWARE_ID,
                    "key_code": key,
                    "machine_code": machine_code
                }
                headers = {"Content-Type": "application/json;charset=UTF-8"}
                self.login_thread = NetworkThread(API_NEW_LOGIN, data, headers)
                self.login_thread.finished.connect(lambda response: self.handle_login_response(response, key))
                self.login_thread.error.connect(self.handle_login_error)
                self.login_thread.start()
            else:
                # 16位卡密直接提示错误
                QMessageBox.critical(self, '登录失败', '卡密不存在')
                self.reset_login_button()

        except Exception as e:
            print(f"登录处理失败: {e}")
            QMessageBox.critical(self, '错误', f'登录处理失败: {str(e)}')
            self.reset_login_button()

    def reset_login_button(self):
        """重置登录按钮状态"""
        try:
            self.login_btn.setEnabled(True)
            self.login_btn.setText('登陆')
        except Exception as e:
            print(f"重置登录按钮失败: {e}")

    def handle_login_error(self, error_msg):
        """处理登录错误"""
        print(f"登录错误: {error_msg}")
        QMessageBox.critical(self, '网络错误', f'网络请求失败，请尝试重新连接！')
        self.reset_login_button()

    def handle_login_response(self, response, key):
        """处理登录响应"""
        try:
            # 添加调试信息
            print(f"登录响应状态码: {response.status_code}")
            print(f"登录响应内容: {response.text}")

            # 检查响应是否为纯数字错误码
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                # 这是一个错误码
                error_code = response_text
                if error_code in NEW_ERROR_MESSAGES:
                    error_msg = NEW_ERROR_MESSAGES[error_code]
                    self.login_status_label.setText(f"登录失败: {error_msg}")
                    self.login_status_label.setStyleSheet("color: red; font-size: 12px;")
                    QMessageBox.critical(self, '登录失败', error_msg)
                else:
                    error_msg = f'登录失败：错误码 {error_code}'
                    self.login_status_label.setText(error_msg)
                    self.login_status_label.setStyleSheet("color: red; font-size: 12px;")
                    QMessageBox.critical(self, '登录失败', error_msg)
                self.reset_login_button()
                return

            # 尝试解析JSON响应
            try:
                response_data = response.json()
            except:
                self.login_status_label.setText("登录失败: 服务器响应格式错误")
                self.login_status_label.setStyleSheet("color: red; font-size: 12px;")
                QMessageBox.critical(self, '登录失败', '服务器响应格式错误')
                self.reset_login_button()
                return

            # 检查新通道成功响应格式：包含token和expires字段
            if "token" in response_data and "expires" in response_data:
                # 新通道登录成功
                token = response_data["token"]
                expiry_time_str = response_data["expires"]

                print(f"登录成功，token: {token}")
                print(f"到期时间: {expiry_time_str}")

                # 设置登录信息
                self.card_key = key
                self.expiry_time = expiry_time_str

                # 获取AES密钥
                self.get_aes_key(key)
                return

            else:
                # 检查是否有错误码
                error_code = str(response_data.get("error", ""))
                if error_code in NEW_ERROR_MESSAGES:
                    error_msg = NEW_ERROR_MESSAGES[error_code]
                    self.login_status_label.setText(f"登录失败: {error_msg}")
                    self.login_status_label.setStyleSheet("color: red; font-size: 12px;")
                    QMessageBox.critical(self, '登录失败', error_msg)
                else:
                    error_msg = response_data.get("message", "登录失败")
                    self.login_status_label.setText(f"登录失败: {error_msg}")
                    self.login_status_label.setStyleSheet("color: red; font-size: 12px;")
                    QMessageBox.critical(self, '登录失败', error_msg)
                self.reset_login_button()

        except Exception as e:
            print(f"处理登录响应失败: {e}")
            QMessageBox.critical(self, '错误', f'处理登录响应失败: {str(e)}')
            self.reset_login_button()

    def get_aes_key(self, key):
        """获取AES加密密钥"""
        try:
            print("开始获取AES密钥")

            # 准备请求数据
            api_url = "http://************/api/verify.php"
            data = {
                "key": key,
                "feature_code": "vmp"
            }

            # 设置请求头
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }

            # 创建并启动网络线程
            self.aes_key_thread = NetworkThread(api_url, data, headers)
            self.aes_key_thread.finished.connect(self.handle_aes_key_response)
            self.aes_key_thread.error.connect(self.handle_aes_key_error)
            self.aes_key_thread.start()

        except Exception as e:
            print(f"获取AES密钥请求失败: {e}")
            # 即使AES密钥获取失败，仍然继续登录流程
            self.complete_login("", "")

    def handle_aes_key_response(self, response):
        """处理AES密钥响应"""
        try:
            response_data = response.json()

            if response_data.get("status") == True:
                # 获取完整密钥
                full_code = response_data.get("data", {}).get("code", "")

                # 分割密钥和初始向量
                if len(full_code) >= 48:
                    aes_key = full_code[:32]
                    aes_iv = full_code[32:48]

                    print(f"AES密钥获取成功")
                    print(f"AES1: {aes_key}")
                    print(f"AES2: {aes_iv}")

                    # 完成登录流程，传递AES密钥
                    self.complete_login(aes_key, aes_iv)
                else:
                    print(f"AES密钥格式错误: {full_code}")
                    # 完成登录流程，但传递空密钥
                    self.complete_login("", "")
            else:
                print(f"获取AES密钥失败: {response_data.get('message', '未知错误')}")
                # 完成登录流程，但传递空密钥
                self.complete_login("", "")

        except Exception as e:
            print(f"处理AES密钥响应失败: {e}")
            # 完成登录流程，但传递空密钥
            self.complete_login("", "")

    def handle_aes_key_error(self, error_msg):
        """处理AES密钥获取错误"""
        print(f"获取AES密钥失败: {error_msg}")
        # 完成登录流程，但传递空密钥
        self.complete_login("", "")

    def complete_login(self, aes_key="", aes_iv=""):
        """完成登录流程"""
        self.aes_key = aes_key
        self.aes_iv = aes_iv
        self.is_logged_in = True

        # 打印AES密钥用于调试
        print(f"=== 登录成功调试信息 ===")
        print(f"卡密: {self.card_key}")
        print(f"到期时间: {self.expiry_time}")
        print(f"AES密钥: {self.aes_key}")
        print(f"AES初始向量: {self.aes_iv}")
        print(f"========================")

        # 保存登录凭据
        self.save_login_credentials()

        # 登录成功后禁用登录按钮，防止重复登录
        self.login_btn.setEnabled(False)
        self.login_btn.setText('已登录')
        self.login_btn.setStyleSheet("background-color: #E0E0E0; color: #808080;")

        # 更新状态标签
        if self.expiry_time:
            self.login_status_label.setText(f"登录成功，到期时间: {self.expiry_time}")
            self.login_status_label.setStyleSheet("color: green; font-size: 12px;")
            message = f"登录成功！\n您的卡密到期时间为: {self.expiry_time}"
        else:
            self.login_status_label.setText("登录成功")
            self.login_status_label.setStyleSheet("color: green; font-size: 12px;")
            message = "登录成功！"

        QMessageBox.information(self, '登录成功', message)

    def unbind(self):
        """处理解绑逻辑"""
        try:
            key = self.card_input.text().strip()
            if not key:
                QMessageBox.warning(self, '提示', '请输入卡密以进行解绑')
                return

            key_length = len(key)

            # 检查卡密长度
            if key_length < 16 or key_length > 32:
                QMessageBox.critical(self, '解绑失败', '卡密不存在')
                return

            # 确认解绑
            reply = QMessageBox.question(self, '确认解绑',
                                       '确定要解绑当前卡密吗？解绑后需要重新登录。',
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)

            if reply != QMessageBox.Yes:
                return

            # 禁用解绑按钮
            self.unbind_btn.setEnabled(False)
            self.unbind_btn.setText('解绑中...')

            # 获取机器码
            machine_code = get_machine_code()

            # 只使用新通道解绑
            if 17 <= key_length <= 32:
                print("使用新通道解绑")
                data = {
                    "software_identifier": SOFTWARE_ID,
                    "key_code": key,
                    "machine_code": machine_code
                }
                headers = {"Content-Type": "application/json;charset=UTF-8"}
                self.unbind_thread = NetworkThread(API_NEW_UNBIND, data, headers)
                self.unbind_thread.finished.connect(self.handle_unbind_response)
                self.unbind_thread.error.connect(self.handle_unbind_error)
                self.unbind_thread.start()
            else:
                # 16位卡密直接提示错误
                QMessageBox.critical(self, '解绑失败', '卡密不存在')
                self.reset_unbind_button()

        except Exception as e:
            print(f"解绑处理失败: {e}")
            QMessageBox.critical(self, '错误', f'解绑处理失败: {str(e)}')
            self.reset_unbind_button()

    def reset_unbind_button(self):
        """重置解绑按钮状态"""
        try:
            self.unbind_btn.setEnabled(True)
            self.unbind_btn.setText('解绑')
        except Exception as e:
            print(f"重置解绑按钮失败: {e}")

    def handle_unbind_error(self, error_msg):
        """处理解绑错误"""
        print(f"解绑错误: {error_msg}")
        QMessageBox.critical(self, '网络错误', f'网络请求失败: {error_msg}')
        self.reset_unbind_button()

    def handle_unbind_response(self, response):
        """处理解绑响应"""
        try:
            # 检查响应是否为纯数字错误码
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                # 这是一个错误码
                error_code = response_text
                if error_code in NEW_ERROR_MESSAGES:
                    QMessageBox.critical(self, '解绑失败', NEW_ERROR_MESSAGES[error_code])
                else:
                    QMessageBox.critical(self, '解绑失败', f'解绑失败：错误码 {error_code}')
                self.reset_unbind_button()
                return

            # 尝试解析JSON响应
            try:
                response_data = response.json()
            except:
                QMessageBox.critical(self, '解绑失败', '服务器响应格式错误')
                self.reset_unbind_button()
                return

            if response_data.get("success") == True:
                # 解绑成功
                remaining_unbinds = response_data.get("remaining_unbinds", 0)

                # 清除登录凭据
                self.clear_login_credentials()

                # 清空输入框
                self.card_input.clear()

                # 重置登录状态
                self.is_logged_in = False
                self.card_key = ""
                self.expiry_time = ""
                self.aes_key = ""
                self.aes_iv = ""

                # 重新启用登录按钮
                self.login_btn.setEnabled(True)
                self.login_btn.setText('登陆')
                self.login_btn.setStyleSheet("")  # 恢复默认样式

                # 更新状态标签
                self.login_status_label.setText(f"解绑成功，剩余解绑次数: {remaining_unbinds}")
                self.login_status_label.setStyleSheet("color: green; font-size: 12px;")

                # 显示解绑成功消息
                QMessageBox.information(self, '解绑成功',
                                      f'解绑成功！剩余解绑次数: {remaining_unbinds}')
            else:
                # 解绑失败
                error_msg = response_data.get("message", "解绑失败")
                self.login_status_label.setText(f"解绑失败: {error_msg}")
                self.login_status_label.setStyleSheet("color: red; font-size: 12px;")
                QMessageBox.critical(self, '解绑失败', error_msg)

            self.reset_unbind_button()

        except Exception as e:
            print(f"处理解绑响应失败: {e}")
            QMessageBox.critical(self, '错误', f'处理解绑响应失败: {str(e)}')
            self.reset_unbind_button()

    def get_selected_functions(self):
        """获取选中的功能"""
        selected_functions = []

        # 遍历所有复选框
        for widget in self.findChildren(QCheckBox):
            if widget.isChecked():
                selected_functions.append(widget.text())

        return selected_functions

    def start_processing(self):
        """开始处理"""
        try:
            # 检查登录状态
            if not self.is_logged_in:
                QMessageBox.warning(self, '提示', '请先登录后再进行处理')
                return

            # 检查是否正在处理
            if self.is_processing:
                QMessageBox.warning(self, '提示', '正在处理中，请等待完成')
                return

            # 检查A素材
            if not self.video_list:
                QMessageBox.warning(self, '提示', '请先选择A素材文件夹')
                return

            # 检查B素材
            if not self.b_material_path:
                QMessageBox.warning(self, '提示', '请先选择B素材路径')
                return

            # 检查输出路径
            if not self.output_dir:
                QMessageBox.warning(self, '提示', '请先选择输出路径')
                return

            # 获取选中的功能
            selected_functions = self.get_selected_functions()

            if not selected_functions:
                QMessageBox.warning(self, '提示', '请至少选择一个功能')
                return

            if len(selected_functions) > 1:
                QMessageBox.warning(self, '提示', '选择的功能无法组合使用，请只选择一个！')
                return

            selected_function = selected_functions[0]
            print(f"选择的功能: {selected_function}")

            # 检查功能是否已实现
            # 一键出同框功能
            tongkuang_functions = ["海盗船", "摩天轮", "过山车", "大摆锤"]

            # 新功能区功能（使用狂鲨处理器，无需服务器验证）
            kuangsha_functions = ["狂鲨", "风暴", "野火", "飞翼", "通灵", "传火", "充能", "咒文"]

            # 新功能区功能（使用鸭嘴兽处理器，无需服务器验证）
            platypus_functions = ["比比东", "新唐门", "武魂殿", "传灵塔"]

            # AB融合功能（前五排）
            ab_functions = [
                "超繁AB", "幻躁AB", "隐身AB", "百万AB", "挂链AB",
                "灰头AB", "小丑AB", "大凡AB", "闪帕AB", "无闪AB",
                "翻牌AB", "成功AB", "慢爆AB", "速爆AB", "赤兔AB",
                "面具2.9", "梨汤AB", "超凡AB", "生而AB", "不凡AB"
            ]

            # 第六行功能（魔法少女处理器）
            mofa_functions = [
                "安琪AB", "跺踩AB", "马刀AB", "子牙AB"
            ]

            # 最后一排功能（赛博AB处理器）
            cyber_functions = [
                "赛博AB", "深渊AB", "起源AB", "合天下"
            ]

            # 新增啸岳功能（啸岳处理器，无需服务器验证）
            xiaoyue_functions = [
                "陨星AB", "烈锋AB", "擎苍AB", "啸岳AB"
            ]

            # Tiktok栏功能（tk爆闪AB处理器）
            baoshan_functions = [
                "爆闪AB", "零式AB", "未来引擎AB"
            ]

            # 小红薯栏功能（红薯处理器）
            hongshu_functions = [
                "红薯", "紫薯", "粉薯", "马铃薯"
            ]

            # 时长选择功能（时长处理器）
            duration_functions = [
                "超凡6秒", "超凡7秒", "爆改6秒", "爆改8秒"
            ]

            # 龙骑3.0力推功能（龙骑处理器，需要服务器验证）
            longqi_functions = [
                "新慢爆", "新翻牌", "新闪帧", "新梨汤", "咪咪", "虾条", "玉溪", "利群"
            ]

            implemented_functions = tongkuang_functions + kuangsha_functions + platypus_functions + ab_functions + mofa_functions + cyber_functions + xiaoyue_functions + baoshan_functions + hongshu_functions + duration_functions + longqi_functions

            if selected_function not in implemented_functions:
                QMessageBox.information(self, '提示', '功能尚未完成，敬请期待！')
                return

            # 新功能区功能和啸岳功能无需服务器验证，直接开始处理
            if selected_function in kuangsha_functions or selected_function in platypus_functions or selected_function in xiaoyue_functions:
                self.start_actual_processing(selected_function)
            else:
                # 只有一键出同框功能使用风火轮二代处理器，统一功能标识：07150001
                # 验证功能权限
                self.verify_feature_permission(selected_function)

        except Exception as e:
            print(f"开始处理失败: {e}")
            QMessageBox.critical(self, '错误', f'开始处理失败: {str(e)}')

    def get_feature_code(self, selected_function):
        """根据功能名获取功能标识"""
        # 第三行功能使用浪漫满屋处理器
        langman_functions = [
            "闪帕AB", "无闪AB", "翻牌AB", "成功AB"
        ]

        # AB融合其他功能使用超凡AB处理器
        ab_functions = [
            "超繁AB", "幻躁AB", "隐身AB", "百万AB", "挂链AB",
            "灰头AB", "小丑AB", "大凡AB", "慢爆AB", "速爆AB", "赤兔AB",
            "面具2.9", "梨汤AB", "超凡AB", "生而AB", "不凡AB"
        ]

        # 第六行功能使用魔法少女处理器
        mofa_functions = [
            "安琪AB", "跺踩AB", "马刀AB", "子牙AB"
        ]

        # 最后一排功能使用赛博AB处理器
        cyber_functions = [
            "赛博AB", "深渊AB", "起源AB", "合天下"
        ]

        # Tiktok栏功能使用tk爆闪AB处理器
        baoshan_functions = [
            "爆闪AB", "零式AB", "未来引擎AB"
        ]

        # 小红薯栏功能使用红薯处理器
        hongshu_functions = [
            "红薯", "紫薯", "粉薯", "马铃薯"
        ]

        # 时长选择功能使用时长处理器
        duration_functions = [
            "超凡6秒", "超凡7秒", "爆改6秒", "爆改8秒"
        ]

        # 龙骑3.0力推功能使用龙骑处理器
        longqi_functions = [
            "新慢爆", "新翻牌", "新闪帧", "新梨汤", "咪咪", "虾条", "玉溪", "利群"
        ]

        if selected_function in langman_functions:
            return "07220001"  # 浪漫满屋功能标识
        elif selected_function in ab_functions:
            return "07170001"  # 超凡AB功能标识
        elif selected_function in mofa_functions:
            return "07080002"  # 魔法少女功能标识
        elif selected_function in cyber_functions:
            return "07170002"  # 赛博AB功能标识
        elif selected_function in baoshan_functions:
            return "07170003"  # tk爆闪AB功能标识
        elif selected_function in hongshu_functions:
            return "07170004"  # 红薯功能标识
        elif selected_function in duration_functions:
            return "eight"  # 时长选择功能标识
        elif selected_function in longqi_functions:
            return "20250722"  # 龙骑3.0力推功能标识
        else:
            return "07150001"  # 一键出同框使用风火轮二代功能标识

    def verify_feature_permission(self, selected_function):
        """验证功能权限"""
        try:
            print(f"开始验证功能权限: {selected_function}")

            # 根据功能确定功能标识
            feature_code = self.get_feature_code(selected_function)

            # 准备请求数据
            api_url = "http://************/api/verify.php"
            data = {
                "key": self.card_key,
                "feature_code": feature_code
            }

            # 设置请求头
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            }

            # 创建并启动网络线程
            self.verify_thread = NetworkThread(api_url, data, headers)
            self.verify_thread.finished.connect(lambda response: self.handle_verify_response(response, selected_function))
            self.verify_thread.error.connect(self.handle_verify_error)
            self.verify_thread.start()



        except Exception as e:
            print(f"验证功能权限失败: {e}")
            QMessageBox.critical(self, '错误', f'验证功能权限失败: {str(e)}')

    def handle_verify_error(self, error_msg):
        """处理验证错误"""
        print(f"验证功能权限网络错误: {error_msg}")
        QMessageBox.critical(self, '网络错误', f'验证功能权限失败: {error_msg}')

    def handle_verify_response(self, response, selected_function):
        """处理验证响应"""
        try:
            print(f"验证响应状态码: {response.status_code}")
            print(f"验证响应内容: {response.text}")

            response_data = response.json()

            if response_data.get("status") == True:
                # 获取加密的配置代码
                encrypted_code = response_data.get("data", {}).get("code", "")
                print(f"验证代码(加密): {encrypted_code}")

                # 解密代码
                self.decrypted_code = None
                if encrypted_code and self.aes_key and self.aes_iv:
                    try:
                        # 使用从登录时获取的 AES key 和 iv
                        aes_key = self.aes_key.encode('utf-8')
                        aes_iv = self.aes_iv.encode('utf-8')

                        cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
                        decrypted = unpad(cipher.decrypt(base64.b64decode(encrypted_code)), AES.block_size)
                        self.decrypted_code = decrypted.decode('utf-8')
                        print(f"验证代码(解密): {self.decrypted_code}")

                        # 检查是否有警告信息
                        if "warning" in response_data.get("data", {}):
                            print(f"警告: {response_data['data']['warning']}")

                        # 开始实际处理
                        self.start_actual_processing(selected_function)

                    except Exception as e:
                        print(f"解密验证代码失败: {str(e)}")
                        QMessageBox.critical(self, '错误', '解密验证代码失败')
                else:
                    print("缺少加密参数，无法解密")
                    QMessageBox.critical(self, '错误', '缺少加密参数，无法解密')
            else:
                error_msg = response_data.get("message", "功能验证失败")
                print(f"功能验证失败: {error_msg}")
                QMessageBox.critical(self, '验证失败', error_msg)

        except Exception as e:
            print(f"处理验证响应失败: {e}")
            QMessageBox.critical(self, '错误', f'处理验证响应失败: {str(e)}')

    def start_actual_processing(self, selected_function):
        """开始实际处理"""
        try:
            print(f"开始实际处理: {selected_function}")

            # 设置处理状态
            self.is_processing = True
            self.process_btn.setEnabled(False)
            self.process_btn.setText('处理中...')



            # 定义新功能区功能列表
            kuangsha_functions = ["狂鲨", "风暴", "野火", "飞翼", "通灵", "传火", "充能", "咒文"]
            platypus_functions = ["比比东", "新唐门", "武魂殿", "传灵塔"]
            xiaoyue_functions = ["陨星AB", "烈锋AB", "擎苍AB", "啸岳AB"]

            # 根据功能选择处理器
            feature_code = self.get_feature_code(selected_function)

            # 新功能区功能使用狂鲨处理器（无需功能代码验证）
            if selected_function in kuangsha_functions:
                print(f"[处理] 创建狂鲨处理器，功能: {selected_function}")
                from Codebase_KuangSha import KuangShaProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = KuangShaProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, selected_function, False
                )
            elif selected_function in platypus_functions:
                print(f"[处理] 创建鸭嘴兽处理器，功能: {selected_function}")
                from Codebase_Platypus1 import PlatypusProcessor

                # 检查B素材是否为图片文件夹
                image_count = self.count_image_files(self.b_material_path)
                if image_count == 0:
                    QMessageBox.warning(self, "错误", "选择的B素材文件夹中没有找到图片文件，请选择包含图片的文件夹")
                    self.reset_processing_state()
                    return

                # 创建鸭嘴兽处理器，使用功能标识20250724的配置
                decrypted_code = '{"step1": {"preset": "ultrafast"}, "step2": {"preset": "ultrafast"}}'

                # 转换video_list格式为字典列表
                video_dict_list = []
                for video_path in self.video_list:
                    video_dict_list.append({
                        'path': video_path,
                        'filename': os.path.basename(video_path)
                    })

                self.worker = PlatypusProcessor(
                    self.ffmpeg_path, video_dict_list, self.b_material_path,
                    self.output_dir, decrypted_code, delete_used_b=False, function_name=selected_function
                )
            elif selected_function in xiaoyue_functions:
                print(f"[处理] 创建啸岳处理器，功能: {selected_function}")
                from Codebase_xiaoyue import XiaoyueProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = XiaoyueProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, selected_function
                )
            elif feature_code == "07220001":
                # 使用浪漫满屋处理器（第三行功能：闪帕AB、无闪AB、翻牌AB、成功AB）
                print("[处理] 创建浪漫满屋处理器")
                from Codebase_LangmanManwu import LangmanManwuProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                # 转换视频列表格式
                a_video_list = []
                for video_path in self.video_list:
                    a_video_list.append({
                        'path': video_path,
                        'filename': os.path.basename(video_path),
                        'duration': self.get_video_duration(video_path)
                    })

                b_video_formatted = []
                for video_path in b_video_list:
                    b_video_formatted.append({
                        'path': video_path,
                        'filename': os.path.basename(video_path),
                        'duration': self.get_video_duration(video_path)
                    })

                self.worker = LangmanManwuProcessor(
                    self.ffmpeg_path, a_video_list, b_video_formatted,
                    self.output_dir, self.decrypted_code, False
                )
            elif feature_code == "07170001":
                # 使用超凡AB处理器
                print("[处理] 创建超凡AB处理器")
                from Codebase_ChaofanAB import ChaofanABProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = ChaofanABProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, self.decrypted_code, False, selected_function
                )
            elif feature_code == "07080002":
                # 使用魔法少女处理器（第六行功能）
                print("[处理] 创建魔法少女处理器")
                from Codebase_mofa import MofaProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = MofaProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, self.decrypted_code, False, "", "", selected_function
                )
            elif feature_code == "07170002":
                # 使用赛博AB处理器（最后一排功能）
                print("[处理] 创建赛博AB处理器")
                from Codebase_CyberAB import CyberABProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = CyberABProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, self.decrypted_code, False, selected_function
                )
            elif feature_code == "07170003":
                # 使用tk爆闪AB处理器（Tiktok栏功能）
                print("[处理] 创建tk爆闪AB处理器")
                from Codebase_BaoshanAB import BaoshanABProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = BaoshanABProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, self.decrypted_code, False, selected_function
                )
            elif feature_code == "07170004":
                # 使用红薯处理器（小红薯栏功能）
                print("[处理] 创建红薯处理器")
                from Codebase_Hongshu import HongshuProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = HongshuProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, self.decrypted_code, False, selected_function
                )
            elif feature_code == "eight":
                # 使用时长选择处理器（时长选择功能）
                print("[处理] 创建时长选择处理器")
                from Codebase_Duration import DurationProcessor

                # 时长选择功能不需要B视频，传递空列表
                b_video_list = []

                self.worker = DurationProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, self.decrypted_code, False, selected_function
                )
            elif feature_code == "20250722":
                # 使用龙骑处理器（龙骑3.0力推功能）
                print(f"[处理] 创建龙骑处理器，功能: {selected_function}")
                from Codebase_Longqi import LongqiProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                # 转换视频列表格式
                a_video_list = []
                for video_path in self.video_list:
                    a_video_list.append({
                        'path': video_path,
                        'filename': os.path.basename(video_path),
                        'duration': self.get_video_duration(video_path)
                    })

                b_video_formatted = []
                for video_path in b_video_list:
                    b_video_formatted.append({
                        'path': video_path,
                        'filename': os.path.basename(video_path),
                        'duration': self.get_video_duration(video_path)
                    })

                self.worker = LongqiProcessor(
                    self.ffmpeg_path, a_video_list, b_video_formatted,
                    self.output_dir, self.decrypted_code, False
                )
            else:
                # 使用风火轮二代处理器（一键出同框）
                print("[处理] 创建风火轮二代处理器")
                self.worker = CustomFenghuoV2Processor(
                    self.ffmpeg_path, self.video_list, self.b_material_path,
                    self.output_dir, self.decrypted_code, False, selected_function, self
                )

            # 连接信号
            self.worker.progress_updated.connect(self.update_progress)
            self.worker.process_finished.connect(self.on_process_finished)

            # 只有CustomFenghuoV2Processor有video_completed信号
            if hasattr(self.worker, 'video_completed'):
                self.worker.video_completed.connect(self.update_video_status)

            # 启动处理
            self.worker.start()

        except Exception as e:
            print(f"开始实际处理失败: {e}")
            QMessageBox.critical(self, '错误', f'开始实际处理失败: {str(e)}')
            self.reset_processing_state()

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        # 不显示进度条上方的文字提示，只更新进度条

    def update_video_status(self, video_index):
        """更新视频表格状态"""
        try:
            if video_index < self.video_table.rowCount():
                self.video_table.setItem(video_index, 3, QTableWidgetItem("已处理"))
                print(f"[状态更新] 第 {video_index + 1} 个视频状态已更新为：已处理")
        except Exception as e:
            print(f"更新视频状态失败: {e}")

    def on_process_finished(self, success, error_msg):
        """处理完成"""
        self.is_processing = False
        self.reset_processing_state()

        if success:
            QMessageBox.information(self, '完成', '所有视频处理完成！')
        else:
            QMessageBox.critical(self, '处理失败', f'处理失败: {error_msg}')

        # 进度条回到0%
        self.progress_bar.setValue(0)

    def reset_processing_state(self):
        """重置处理状态"""
        self.process_btn.setEnabled(True)
        self.process_btn.setText('确定处理')

    # 浪漫满屋处理器相关方法
    def select_langman_a_folder(self):
        """选择浪漫满屋A视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择A视频文件夹")
        if folder:
            self.langman_a_path.setText(folder)
            self.langman_a_video_list = self.get_video_files_from_folder(folder)
            print(f"[浪漫满屋] 选择A视频文件夹: {folder}, 找到 {len(self.langman_a_video_list)} 个视频")

    def select_langman_b_folder(self):
        """选择浪漫满屋B视频文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择B视频文件夹")
        if folder:
            self.langman_b_path.setText(folder)
            self.langman_b_video_list = self.get_video_files_from_folder(folder)
            print(f"[浪漫满屋] 选择B视频文件夹: {folder}, 找到 {len(self.langman_b_video_list)} 个视频")

    def select_langman_output_folder(self):
        """选择浪漫满屋输出文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.langman_output_path.setText(folder)
            self.langman_output_dir = folder
            print(f"[浪漫满屋] 选择输出文件夹: {folder}")

    def start_langman_processing(self):
        """开始浪漫满屋处理"""
        try:
            # 检查输入
            if not self.langman_a_video_list:
                QMessageBox.warning(self, "警告", "请先选择A视频文件夹！")
                return

            if not self.langman_b_video_list:
                QMessageBox.warning(self, "警告", "请先选择B视频文件夹！")
                return

            if not self.langman_output_dir:
                QMessageBox.warning(self, "警告", "请先选择输出文件夹！")
                return

            # 检查登录状态
            if not hasattr(self, 'decrypted_code') or not self.decrypted_code:
                QMessageBox.warning(self, "警告", "请先登录软件！")
                return

            # 设置处理状态
            self.langman_is_processing = True
            self.langman_process_btn.setEnabled(False)
            self.langman_process_btn.setText('处理中...')
            self.langman_status.setText("正在初始化...")
            self.langman_progress.setValue(0)

            # 创建浪漫满屋处理器
            print("[浪漫满屋] 创建浪漫满屋处理器")
            from Codebase_LangmanManwu import LangmanManwuProcessor

            # 转换视频列表格式
            a_video_list = []
            for video_path in self.langman_a_video_list:
                a_video_list.append({
                    'path': video_path,
                    'filename': os.path.basename(video_path),
                    'duration': self.get_video_duration(video_path)
                })

            b_video_list = []
            for video_path in self.langman_b_video_list:
                b_video_list.append({
                    'path': video_path,
                    'filename': os.path.basename(video_path),
                    'duration': self.get_video_duration(video_path)
                })

            self.langman_worker = LangmanManwuProcessor(
                ffmpeg_path=os.path.join(os.path.dirname(__file__), "bin", "ffmpeg.exe"),
                a_video_list=a_video_list,
                b_video_list=b_video_list,
                output_dir=self.langman_output_dir,
                config_code=self.decrypted_code,
                delete_used_b=self.langman_delete_b.isChecked()
            )

            # 连接信号
            self.langman_worker.progress_updated.connect(self.update_langman_progress)
            self.langman_worker.process_finished.connect(self.on_langman_finished)

            # 启动处理
            self.langman_worker.start()
            print(f"[浪漫满屋] 开始处理 {len(a_video_list)} 个A视频和 {len(b_video_list)} 个B视频")

        except Exception as e:
            print(f"[浪漫满屋] 启动处理失败: {e}")
            QMessageBox.critical(self, "错误", f"启动处理失败: {str(e)}")
            self.reset_langman_state()

    def update_langman_progress(self, progress, status):
        """更新浪漫满屋处理进度"""
        self.langman_progress.setValue(progress)
        self.langman_status.setText(status)

    def on_langman_finished(self, success, message):
        """浪漫满屋处理完成"""
        self.reset_langman_state()

        if success:
            QMessageBox.information(self, "处理完成", message)
            self.langman_status.setText("处理完成")
        else:
            QMessageBox.critical(self, "处理失败", message)
            self.langman_status.setText("处理失败")

    def reset_langman_state(self):
        """重置浪漫满屋处理状态"""
        self.langman_is_processing = False
        self.langman_process_btn.setEnabled(True)
        self.langman_process_btn.setText('开始浪漫满屋处理')
        if hasattr(self, 'langman_worker') and self.langman_worker:
            self.langman_worker = None

    def start_new_processing(self):
        """开始新功能区处理"""
        try:
            # 获取选中的功能
            selected_function = None
            new_functions = ["狂鲨", "风暴", "野火", "飞翼", "通灵", "传火", "充能", "咒文", "比比东", "新唐门", "武魂殿", "传灵塔"]

            # 查找所有QCheckBox并检查哪个被选中
            for widget in self.findChildren(QCheckBox):
                if widget.text() in new_functions and widget.isChecked():
                    if selected_function is None:
                        selected_function = widget.text()
                    else:
                        # 如果有多个选中，提示用户
                        QMessageBox.warning(self, "警告", "请只选择一个功能！")
                        return

            if not selected_function:
                QMessageBox.warning(self, "警告", "请先选择一个功能！")
                return

            print(f"[新功能区] 选择的功能: {selected_function}")

            # 调用主处理逻辑
            self.start_processing_with_function(selected_function)

        except Exception as e:
            print(f"[新功能区] 启动处理失败: {e}")
            QMessageBox.critical(self, "错误", f"启动处理失败: {str(e)}")

    def start_processing_with_function(self, selected_function):
        """使用指定功能开始处理"""
        try:
            # 检查登录状态
            if not self.is_logged_in:
                QMessageBox.warning(self, '提示', '请先登录后再进行处理')
                return

            # 检查输入
            if not self.video_list:
                QMessageBox.warning(self, "警告", "请先选择A视频文件夹！")
                return

            if not self.b_material_path:
                QMessageBox.warning(self, "警告", "请先选择B素材文件夹！")
                return

            if not self.output_dir:
                QMessageBox.warning(self, "警告", "请先选择输出文件夹！")
                return

            print(f"[新功能区] 开始处理，功能: {selected_function}")

            # 设置处理状态
            self.is_processing = True
            self.new_process_btn.setEnabled(False)
            self.new_process_btn.setText('处理中...')

            # 定义新功能区功能列表
            kuangsha_functions = ["狂鲨", "风暴", "野火", "飞翼", "通灵", "传火", "充能", "咒文"]
            platypus_functions = ["比比东", "新唐门", "武魂殿", "传灵塔"]
            xiaoyue_functions = ["陨星AB", "烈锋AB", "擎苍AB", "啸岳AB"]

            # 新功能区功能使用狂鲨处理器（无需功能代码验证）
            if selected_function in kuangsha_functions:
                print(f"[处理] 创建狂鲨处理器，功能: {selected_function}")
                from Codebase_KuangSha import KuangShaProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = KuangShaProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, selected_function, False
                )

                # 连接信号
                self.worker.progress_updated.connect(self.update_progress)
                self.worker.process_finished.connect(self.on_new_process_finished)

                # 启动处理
                self.worker.start()
                print(f"[新功能区] 开始处理 {len(self.video_list)} 个A视频和 {len(b_video_list)} 个B视频")
            elif selected_function in platypus_functions:
                print(f"[处理] 创建鸭嘴兽处理器，功能: {selected_function}")
                from Codebase_Platypus1 import PlatypusProcessor

                # 检查B素材是否为图片文件夹
                image_count = self.count_image_files(self.b_material_path)
                if image_count == 0:
                    QMessageBox.warning(self, "错误", "选择的B素材文件夹中没有找到图片文件，请选择包含图片的文件夹")
                    self.reset_new_processing_state()
                    return

                # 创建鸭嘴兽处理器，使用功能标识20250724的配置
                decrypted_code = '{"step1": {"preset": "ultrafast"}, "step2": {"preset": "ultrafast"}}'

                # 转换video_list格式为字典列表
                video_dict_list = []
                for video_path in self.video_list:
                    video_dict_list.append({
                        'path': video_path,
                        'filename': os.path.basename(video_path)
                    })

                self.worker = PlatypusProcessor(
                    self.ffmpeg_path, video_dict_list, self.b_material_path,
                    self.output_dir, decrypted_code, delete_used_b=False, function_name=selected_function
                )

                # 连接信号
                self.worker.progress_updated.connect(self.update_progress)
                self.worker.process_finished.connect(self.on_new_process_finished)

                # 启动处理
                self.worker.start()
                print(f"[新功能区] 开始处理 {len(self.video_list)} 个A视频和 {image_count} 个图片")
            elif selected_function in xiaoyue_functions:
                print(f"[处理] 创建啸岳处理器，功能: {selected_function}")
                from Codebase_xiaoyue import XiaoyueProcessor

                # 获取B视频列表
                b_video_list = self.get_video_files_from_folder(self.b_material_path)

                self.worker = XiaoyueProcessor(
                    self.ffmpeg_path, self.video_list, b_video_list,
                    self.output_dir, selected_function
                )

                # 连接信号
                self.worker.progress_updated.connect(self.update_progress)
                self.worker.process_finished.connect(self.on_new_process_finished)

                # 启动处理
                self.worker.start()
                print(f"[啸岳] 开始处理 {len(self.video_list)} 个A视频和 {len(b_video_list)} 个B视频")
            else:
                QMessageBox.warning(self, "警告", "未知的功能类型！")
                self.reset_new_processing_state()

        except Exception as e:
            print(f"[新功能区] 处理异常: {e}")
            QMessageBox.critical(self, "错误", f"处理过程中发生错误: {str(e)}")
            self.reset_new_processing_state()

    def on_new_process_finished(self, success, message):
        """新功能区处理完成"""
        self.reset_new_processing_state()

        if success:
            QMessageBox.information(self, "处理完成", message)
        else:
            QMessageBox.critical(self, "处理失败", message)

    def reset_new_processing_state(self):
        """重置新功能区处理状态"""
        self.is_processing = False
        if hasattr(self, 'new_process_btn'):
            self.new_process_btn.setEnabled(True)
            self.new_process_btn.setText('确定处理')
        if hasattr(self, 'worker') and self.worker:
            self.worker = None


def main():
    """程序入口"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion样式作为基础

    window = ChaofanMainWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
