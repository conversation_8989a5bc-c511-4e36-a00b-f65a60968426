#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
释永信-业内独创视频号爆流 打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def build_exe():
    """打包为exe文件"""
    
    # 检查必要文件
    required_files = [
        "释永信_业内独创视频号爆流.py",
        "Codebase_shiyongxin.py"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    print("🙏 开始打包释永信-业内独创视频号爆流...")
    
    # PyInstaller打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 无控制台窗口
        "--name=释永信_业内独创视频号爆流",  # 输出文件名
        "--icon=buddha.ico",            # 图标文件（如果有的话）
        "--add-data=Codebase_shiyongxin.py;.",  # 添加依赖文件
        "--hidden-import=cv2",          # 隐式导入
        "--hidden-import=numpy",
        "--hidden-import=requests",
        "--hidden-import=PyQt5",
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtGui", 
        "--hidden-import=PyQt5.QtWidgets",
        "--collect-all=cv2",            # 收集cv2所有模块
        "--exclude-module=tkinter",     # 排除不需要的模块
        "--exclude-module=matplotlib",
        "--exclude-module=PIL",
        "--clean",                      # 清理临时文件
        "释永信_业内独创视频号爆流.py"
    ]
    
    try:
        print("📿 正在执行打包命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        print(f"📁 输出文件位置: dist/释永信_业内独创视频号爆流.exe")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ 未找到PyInstaller，请先安装: pip install pyinstaller")
        return False

def create_spec_file():
    """创建自定义spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['释永信_业内独创视频号爆流.py'],
    pathex=[],
    binaries=[],
    datas=[('Codebase_shiyongxin.py', '.')],
    hiddenimports=[
        'cv2',
        'numpy', 
        'requests',
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'hashlib',
        'subprocess',
        'json',
        'logging',
        'tempfile',
        'threading',
        'concurrent.futures'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'PIL'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='释永信_业内独创视频号爆流',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='buddha.ico'  # 如果有图标文件
)
'''
    
    with open('释永信_爆流.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("📜 已创建spec文件: 释永信_爆流.spec")

def install_requirements():
    """安装必要的依赖"""
    requirements = [
        "pyinstaller",
        "opencv-python", 
        "numpy",
        "requests",
        "PyQt5"
    ]
    
    print("🔧 正在安装必要依赖...")
    for req in requirements:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", req], check=True)
            print(f"✅ {req} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {req} 安装失败")

def main():
    """主函数"""
    print("🙏 释永信-业内独创视频号爆流 打包工具")
    print("=" * 50)
    
    choice = input("""
请选择操作：
1. 安装依赖
2. 创建spec文件
3. 直接打包
4. 使用spec文件打包
5. 全部执行

请输入选择 (1-5): """).strip()
    
    if choice == "1":
        install_requirements()
    elif choice == "2":
        create_spec_file()
    elif choice == "3":
        build_exe()
    elif choice == "4":
        create_spec_file()
        try:
            subprocess.run(["pyinstaller", "释永信_爆流.spec"], check=True)
            print("✅ 使用spec文件打包成功！")
        except subprocess.CalledProcessError as e:
            print(f"❌ 打包失败: {e}")
    elif choice == "5":
        install_requirements()
        create_spec_file()
        build_exe()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
