# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-W', 'ignore', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\BACKEN~1.SCO', '--jobs', '20', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build', 'python_version=3.12', 'python_prefix=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'assume_yes_for_downloads=true', 'console_mode=disable', 'noelf_mode=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=649', 'result_exe=E:\\MONEYC~1\\dist\\释永信~1.DIS\\释永信_业内独创视频号爆流.dll', 'frozen_modules=155', 'python_sysflag_no_site=true'],
    env={'123PAN': 'D:\\Software\\123pan\\123pan.exe','ALLUSERSPROFILE': 'C:\\ProgramData','APPCODE_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\appcode.vmoptions','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','CHROME_CRASHPAD_PIPE_NAME': '\\\\.\\pipe\\crashpad_12920_CZEGWWCCLZSPSFEN','CLION_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\clion.vmoptions','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMPUTERNAME': 'DESKTOP-419NV80','COMSPEC': 'C:\\Windows\\system32\\cmd.exe','DATAGRIP_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\datagrip.vmoptions','DATASPELL_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\dataspell.vmoptions','DEVECOSTUDIO_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\devecostudio.vmoptions','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','EFC_25036_1262719628': '1','EFC_25036_1592913036': '1','EFC_25036_2283032206': '1','EFC_25036_2775293581': '1','EFC_25036_3789132940': '1','FPS_BROWSER_APP_PROFILE_STRING': 'Internet Explorer','FPS_BROWSER_USER_PROFILE_STRING': 'Default','GATEWAY_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\gateway.vmoptions','GOLAND_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\goland.vmoptions','HOMEDRIVE': 'C:','HOMEPATH': '\\Users\\admin','IDEA_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\idea.vmoptions','INTELLIJ IDEA': 'D:\\Program Files\\IntelliJ IDEA 2024.2.4\\bin;','JAVA_HOME': 'D:\\developTools\\Java\\jdk-17\\','JETBRAINSCLIENT_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\jetbrainsclient.vmoptions','JETBRAINS_CLIENT_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\jetbrains_client.vmoptions','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','LOGONSERVER': '\\\\DESKTOP-419NV80','NUMBER_OF_PROCESSORS': '20','ONEDRIVE': 'C:\\Users\\<USER>\\OneDrive','ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined','OS': 'Windows_NT','PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\cv2\\../../x64/vc17/bin;D:\\developTools\\Java\\jdk-17\\\\bin;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Bandizip\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\MongoDB\\Server\\7.0\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\UGit\\app-5.36.0\\resources\\app\\git\\cmd;C:\\Users\\<USER>\\AppData\\Local\\UGit\\bin;D:\\Program Files\\IntelliJ IDEA 2024.2.4\\bin;;D:\\Program Files\\PyCharm 2024.2.4\\bin;;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\scripts\\noConfigScripts','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL','PHPSTORM_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\phpstorm.vmoptions','PROCESSOR_ARCHITECTURE': 'AMD64','PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 183 Stepping 1, GenuineIntel','PROCESSOR_LEVEL': '6','PROCESSOR_REVISION': 'b701','PROGRAMDATA': 'C:\\ProgramData','PROGRAMFILES': 'C:\\Program Files','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PROGRAMW6432': 'C:\\Program Files','PROMPT': '$P$G','PSMODULEPATH': 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules','PUBLIC': 'C:\\Users\\<USER>\\Program Files\\PyCharm 2024.2.4\\bin;','PYCHARM_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\pycharm.vmoptions','PYTHONHASHSEED': '0','RIDER_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\rider.vmoptions','RUBYMINE_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\rubymine.vmoptions','SESSIONNAME': 'Console','STUDIO_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\studio.vmoptions','SYSTEMDRIVE': 'C:','SYSTEMROOT': 'C:\\Windows','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','UGIT_HOME': 'C:\\Users\\<USER>\\AppData\\Local\\UGit\\app-5.36.0','USERDOMAIN': 'DESKTOP-419NV80','USERDOMAIN_ROAMINGPROFILE': 'DESKTOP-419NV80','USERNAME': 'admin','USERPROFILE': 'C:\\Users\\<USER>\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\webide.vmoptions','WEBSTORM_VM_OPTIONS': 'D:\\Program Files\\crack\\ja-netfilter-all\\vmoptions\\webstorm.vmoptions','WINDIR': 'C:\\Windows','LANG': 'zh_CN.UTF-8','PYDEVD_DISABLE_FILE_VALIDATION': '1','BUNDLED_DEBUGPY_PATH': 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\libs\\debugpy','NUITKA_PYTHON_EXE_PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe','NUITKA_PACKAGE_DIR': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)