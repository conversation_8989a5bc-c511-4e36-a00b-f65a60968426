AS=as
CC="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe"
CCACHE_DIR=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
CCACHE_LOGFILE=E:\MONEYC~1\dist\SHIYON~1.ONE\ccache-33200.txt
CCCOM=$CC -o $TARGET -c $CFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CFILESUFFIX=.c
CPPDEFINES=['_NUITKA_STANDALONE_MODE', '_NUITKA_ONEFILE_MODE', '_NUITKA_ONEFILE_TEMP_BOOL', '_NUITKA_DLL_MODE', '_NUITKA_ONEFILE_DLL_MODE', '_WIN32_WINNT=0x0601', '__NUITKA_NO_ASSERT__', '_NUITKA_EXE_MODE', '_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1']
CPPDEFPREFIX=-D
CPPDEFSUFFIX=
CPPPATH=['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib', '.', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd']
CPPSUFFIXES=['.c', '.C', '.cxx', '.cpp', '.c++', '.cc', '.h', '.H', '.hxx', '.hpp', '.hh', '.F', '.fpp', '.FPP', '.m', '.mm', '.S', '.spp', '.SPP', '.sx']
CXX="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe"
CXXCOM=$CXX -o $TARGET -c $CXXFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CXXFILESUFFIX=.cc
HOST_ARCH=x86_64
HOST_OS=win32
INCPREFIX=-I
INCSUFFIX=
LDMODULE=$SHLINK
LDMODULEFLAGS=$SHLINKFLAGS
LDMODULENAME=${LDMODULEPREFIX}$_get_ldmodule_stem${_LDMODULESUFFIX}
LDMODULEPREFIX=$SHLIBPREFIX
LDMODULESUFFIX=$SHLIBSUFFIX
LDMODULEVERSION=$SHLIBVERSION
LDMODULE_NOVERSION_SYMLINK=$_get_shlib_dir${LDMODULEPREFIX}$_get_ldmodule_stem${LDMODULESUFFIX}
LDMODULE_SONAME_SYMLINK=$_get_shlib_dir$_LDMODULESONAME
LIBDIRPREFIX=-L
LIBDIRSUFFIX=
LIBLINKPREFIX=-l
LIBLINKSUFFIX=
LIBPATH=[]
LIBPREFIX=lib
LIBPREFIXES=['$LIBPREFIX']
LIBS=['PsApi']
LIBSUFFIX=.a
LIBSUFFIXES=['$LIBSUFFIX']
LINK="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe"
LINKCOM=$LINK -o $TARGET $LINKFLAGS $__RPATH $SOURCES $_LIBDIRFLAGS $_LIBFLAGS
OBJPREFIX=
OBJSUFFIX=.o
PLATFORM=win32
PROGPREFIX=
PROGSUFFIX=.exe
RC=windres
RCCOM=$RC $_CPPDEFFLAGS $RCINCFLAGS ${RCINCPREFIX} ${SOURCE.dir} $RCFLAGS -i $SOURCE -o $TARGET
RCINCFLAGS=${_concat(RCINCPREFIX, CPPPATH, RCINCSUFFIX, __env__, RDirs, TARGET, SOURCE, affect_signature=False)}
RCINCPREFIX=--include-dir 
RCINCSUFFIX=
RPATHPREFIX=-Wl,-rpath=
RPATHSUFFIX=
SHCC=$CC
SHCCCOM=$SHCC -o $TARGET -c $SHCFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHCXX=$CXX
SHCXXCOM=$SHCXX -o $TARGET -c $SHCXXFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHELL=C:\Windows\System32\cmd.exe
SHLIBNAME=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${_SHLIBSUFFIX}
SHLIBPREFIX=
SHLIBSONAMEFLAGS=-Wl,-soname=$_SHLIBSONAME
SHLIBSUFFIX=.dll
SHLIB_NOVERSION_SYMLINK=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${SHLIBSUFFIX}
SHLIB_SONAME_SYMLINK=${_get_shlib_dir}$_SHLIBSONAME
SHLINK=$LINK
SHOBJPREFIX=$OBJPREFIX
SHOBJSUFFIX=.o
TARGET_ARCH=x86_64
TEMPFILEARGJOIN= 
TEMPFILEPREFIX=@
TOOLS=['mingw', 'gcc', 'g++', 'gnulink']
WINDOWSDEFPREFIX=
WINDOWSDEFSUFFIX=.def
gcc_mode=True
clang_mode=False
msvc_mode=False
mingw_mode=True
clangcl_mode=False
PATH=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\cv2\../../x64/vc17/bin;D:\developTools\Java\jdk-17\\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Bandizip\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files\MongoDB\Server\7.0\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\UGit\app-5.36.0\resources\app\git\cmd;C:\Users\<USER>\AppData\Local\UGit\bin;D:\Program Files\IntelliJ IDEA 2024.2.4\bin;D:\Program Files\PyCharm 2024.2.4\bin;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.10.0-win32-x64\bundled\scripts\noConfigScripts
TARGET=E:\MONEYC~1\dist\shiyongxinMain.exe
