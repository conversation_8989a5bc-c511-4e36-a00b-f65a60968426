[2025-07-28T18:10:08.427926 20600] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T18:10:08.427993 20600] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T18:10:08.428008 20600] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T18:10:08.428008 20600] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T18:10:08.428008 20600] Config: (default) base_dir = 
[2025-07-28T18:10:08.428008 20600] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T18:10:08.428008 20600] Config: (default) compiler = 
[2025-07-28T18:10:08.428008 20600] Config: (default) compiler_check = mtime
[2025-07-28T18:10:08.428008 20600] Config: (default) compiler_type = auto
[2025-07-28T18:10:08.428008 20600] Config: (default) compression = true
[2025-07-28T18:10:08.428008 20600] Config: (default) compression_level = 0
[2025-07-28T18:10:08.428008 20600] Config: (default) cpp_extension = 
[2025-07-28T18:10:08.428008 20600] Config: (default) debug = false
[2025-07-28T18:10:08.428008 20600] Config: (default) debug_dir = 
[2025-07-28T18:10:08.428008 20600] Config: (default) debug_level = 2
[2025-07-28T18:10:08.428008 20600] Config: (default) depend_mode = false
[2025-07-28T18:10:08.428008 20600] Config: (default) direct_mode = true
[2025-07-28T18:10:08.428008 20600] Config: (default) disable = false
[2025-07-28T18:10:08.428008 20600] Config: (default) extra_files_to_hash = 
[2025-07-28T18:10:08.428008 20600] Config: (default) file_clone = false
[2025-07-28T18:10:08.428008 20600] Config: (default) hard_link = false
[2025-07-28T18:10:08.428008 20600] Config: (default) hash_dir = true
[2025-07-28T18:10:08.428008 20600] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T18:10:08.428008 20600] Config: (default) ignore_options = 
[2025-07-28T18:10:08.428008 20600] Config: (default) inode_cache = false
[2025-07-28T18:10:08.428008 20600] Config: (default) keep_comments_cpp = false
[2025-07-28T18:10:08.428008 20600] Config: (environment) log_file = E:\MONEYC~1\dist\SHIYON~1.ONE\ccache-48556.txt
[2025-07-28T18:10:08.428008 20600] Config: (default) max_files = 0
[2025-07-28T18:10:08.428008 20600] Config: (default) max_size = 5.0 GiB
[2025-07-28T18:10:08.428008 20600] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T18:10:08.428008 20600] Config: (default) namespace = 
[2025-07-28T18:10:08.428008 20600] Config: (default) path = 
[2025-07-28T18:10:08.428008 20600] Config: (default) pch_external_checksum = false
[2025-07-28T18:10:08.428008 20600] Config: (default) prefix_command = 
[2025-07-28T18:10:08.428008 20600] Config: (default) prefix_command_cpp = 
[2025-07-28T18:10:08.428008 20600] Config: (default) read_only = false
[2025-07-28T18:10:08.428008 20600] Config: (default) read_only_direct = false
[2025-07-28T18:10:08.428008 20600] Config: (default) recache = false
[2025-07-28T18:10:08.428008 20600] Config: (default) remote_only = false
[2025-07-28T18:10:08.428008 20600] Config: (default) remote_storage = 
[2025-07-28T18:10:08.428008 20600] Config: (default) reshare = false
[2025-07-28T18:10:08.428008 20600] Config: (default) run_second_cpp = true
[2025-07-28T18:10:08.428008 20600] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T18:10:08.428008 20600] Config: (default) stats = true
[2025-07-28T18:10:08.428008 20600] Config: (default) stats_log = 
[2025-07-28T18:10:08.428008 20600] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T18:10:08.428008 20600] Config: (default) umask = 
[2025-07-28T18:10:08.428068 20600] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=20 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-07-28T18:10:08.437498 20600] Hostname: DESKTOP-419NV80
[2025-07-28T18:10:08.437523 20600] Working directory: E:\MONEYC~1\dist\SHIYON~1.ONE
[2025-07-28T18:10:08.437544 20600] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T18:10:08.437549 20600] Compiler type: gcc
[2025-07-28T18:10:08.437753 20600] Detected input file: static_src\OnefileBootstrap.c
[2025-07-28T18:10:08.437858 20600] Source file: static_src\OnefileBootstrap.c
[2025-07-28T18:10:08.437862 20600] Object file: static_src\OnefileBootstrap.o
[2025-07-28T18:10:08.437981 20600] Trying direct lookup
[2025-07-28T18:10:08.438148 20600] Manifest key: d795ugteehjsuhv8ult9la8so4c9th7pe
[2025-07-28T18:10:08.438220 20600] Retrieved d795ugteehjsuhv8ult9la8so4c9th7pe from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/7/95ugteehjsuhv8ult9la8so4c9th7peM)
[2025-07-28T18:10:08.463218 20600] Got result key from manifest
[2025-07-28T18:10:08.463247 20600] Result key: ec03ukuerabjglahs85qbqsh9066nvd9e
[2025-07-28T18:10:08.463464 20600] Retrieved ec03ukuerabjglahs85qbqsh9066nvd9e from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/c/03ukuerabjglahs85qbqsh9066nvd9eR)
[2025-07-28T18:10:08.463999 20600] Reading embedded entry #0 .o (612715 bytes)
[2025-07-28T18:10:08.464019 20600] Writing to static_src\OnefileBootstrap.o
[2025-07-28T18:10:08.464387 20600] Succeeded getting cached result
[2025-07-28T18:10:08.464501 20600] Result: direct_cache_hit
[2025-07-28T18:10:08.464505 20600] Result: local_storage_hit
[2025-07-28T18:10:08.464508 20600] Result: local_storage_read_hit
[2025-07-28T18:10:08.464510 20600] Result: local_storage_read_hit
[2025-07-28T18:10:08.464528 20600] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
[2025-07-28T18:10:08.464956 20600] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
[2025-07-28T18:10:08.465327 20600] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
[2025-07-28T18:10:08.465411 20600] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/8/stats.lock
