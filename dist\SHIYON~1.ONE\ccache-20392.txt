[2025-07-28T18:24:41.266939 5800 ] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T18:24:41.267024 5800 ] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T18:24:41.267038 5800 ] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T18:24:41.267038 5800 ] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) base_dir = 
[2025-07-28T18:24:41.267038 5800 ] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T18:24:41.267038 5800 ] Config: (default) compiler = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) compiler_check = mtime
[2025-07-28T18:24:41.267038 5800 ] Config: (default) compiler_type = auto
[2025-07-28T18:24:41.267038 5800 ] Config: (default) compression = true
[2025-07-28T18:24:41.267038 5800 ] Config: (default) compression_level = 0
[2025-07-28T18:24:41.267038 5800 ] Config: (default) cpp_extension = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) debug = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) debug_dir = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) debug_level = 2
[2025-07-28T18:24:41.267038 5800 ] Config: (default) depend_mode = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) direct_mode = true
[2025-07-28T18:24:41.267038 5800 ] Config: (default) disable = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) extra_files_to_hash = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) file_clone = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) hard_link = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) hash_dir = true
[2025-07-28T18:24:41.267038 5800 ] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) ignore_options = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) inode_cache = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) keep_comments_cpp = false
[2025-07-28T18:24:41.267038 5800 ] Config: (environment) log_file = E:\MONEYC~1\dist\SHIYON~1.ONE\ccache-20392.txt
[2025-07-28T18:24:41.267038 5800 ] Config: (default) max_files = 0
[2025-07-28T18:24:41.267038 5800 ] Config: (default) max_size = 5.0 GiB
[2025-07-28T18:24:41.267038 5800 ] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T18:24:41.267038 5800 ] Config: (default) namespace = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) path = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) pch_external_checksum = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) prefix_command = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) prefix_command_cpp = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) read_only = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) read_only_direct = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) recache = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) remote_only = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) remote_storage = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) reshare = false
[2025-07-28T18:24:41.267038 5800 ] Config: (default) run_second_cpp = true
[2025-07-28T18:24:41.267038 5800 ] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T18:24:41.267038 5800 ] Config: (default) stats = true
[2025-07-28T18:24:41.267038 5800 ] Config: (default) stats_log = 
[2025-07-28T18:24:41.267038 5800 ] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T18:24:41.267038 5800 ] Config: (default) umask = 
[2025-07-28T18:24:41.267106 5800 ] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=20 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-07-28T18:24:41.269842 5800 ] Hostname: DESKTOP-419NV80
[2025-07-28T18:24:41.269871 5800 ] Working directory: E:\MONEYC~1\dist\SHIYON~1.ONE
[2025-07-28T18:24:41.269895 5800 ] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T18:24:41.269900 5800 ] Compiler type: gcc
[2025-07-28T18:24:41.270112 5800 ] Detected input file: static_src\OnefileBootstrap.c
[2025-07-28T18:24:41.270213 5800 ] Source file: static_src\OnefileBootstrap.c
[2025-07-28T18:24:41.270218 5800 ] Object file: static_src\OnefileBootstrap.o
[2025-07-28T18:24:41.270360 5800 ] Trying direct lookup
[2025-07-28T18:24:41.270520 5800 ] Manifest key: d795ugteehjsuhv8ult9la8so4c9th7pe
[2025-07-28T18:24:41.270596 5800 ] Retrieved d795ugteehjsuhv8ult9la8so4c9th7pe from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/7/95ugteehjsuhv8ult9la8so4c9th7peM)
[2025-07-28T18:24:41.295822 5800 ] Got result key from manifest
[2025-07-28T18:24:41.295854 5800 ] Result key: ec03ukuerabjglahs85qbqsh9066nvd9e
[2025-07-28T18:24:41.296052 5800 ] Retrieved ec03ukuerabjglahs85qbqsh9066nvd9e from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/c/03ukuerabjglahs85qbqsh9066nvd9eR)
[2025-07-28T18:24:41.296540 5800 ] Reading embedded entry #0 .o (612715 bytes)
[2025-07-28T18:24:41.296554 5800 ] Writing to static_src\OnefileBootstrap.o
[2025-07-28T18:24:41.296914 5800 ] Succeeded getting cached result
[2025-07-28T18:24:41.297024 5800 ] Result: direct_cache_hit
[2025-07-28T18:24:41.297028 5800 ] Result: local_storage_hit
[2025-07-28T18:24:41.297031 5800 ] Result: local_storage_read_hit
[2025-07-28T18:24:41.297033 5800 ] Result: local_storage_read_hit
[2025-07-28T18:24:41.297052 5800 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/8/stats.lock
[2025-07-28T18:24:41.297453 5800 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/8/stats.lock
[2025-07-28T18:24:41.297785 5800 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/8/stats.lock
[2025-07-28T18:24:41.297843 5800 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/8/stats.lock
