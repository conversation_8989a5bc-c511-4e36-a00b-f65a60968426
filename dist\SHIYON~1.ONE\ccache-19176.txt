[2025-07-28T17:47:44.771872 45836] === CCACHE 4.10.2 STARTED =========================================
[2025-07-28T17:47:44.771941 45836] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-07-28T17:47:44.771953 45836] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-07-28T17:47:44.771953 45836] Config: (default) absolute_paths_in_stderr = false
[2025-07-28T17:47:44.771953 45836] Config: (default) base_dir = 
[2025-07-28T17:47:44.771953 45836] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-07-28T17:47:44.771953 45836] Config: (default) compiler = 
[2025-07-28T17:47:44.771953 45836] Config: (default) compiler_check = mtime
[2025-07-28T17:47:44.771953 45836] Config: (default) compiler_type = auto
[2025-07-28T17:47:44.771953 45836] Config: (default) compression = true
[2025-07-28T17:47:44.771953 45836] Config: (default) compression_level = 0
[2025-07-28T17:47:44.771953 45836] Config: (default) cpp_extension = 
[2025-07-28T17:47:44.771953 45836] Config: (default) debug = false
[2025-07-28T17:47:44.771953 45836] Config: (default) debug_dir = 
[2025-07-28T17:47:44.771953 45836] Config: (default) debug_level = 2
[2025-07-28T17:47:44.771953 45836] Config: (default) depend_mode = false
[2025-07-28T17:47:44.771953 45836] Config: (default) direct_mode = true
[2025-07-28T17:47:44.771953 45836] Config: (default) disable = false
[2025-07-28T17:47:44.771953 45836] Config: (default) extra_files_to_hash = 
[2025-07-28T17:47:44.771953 45836] Config: (default) file_clone = false
[2025-07-28T17:47:44.771953 45836] Config: (default) hard_link = false
[2025-07-28T17:47:44.771953 45836] Config: (default) hash_dir = true
[2025-07-28T17:47:44.771953 45836] Config: (default) ignore_headers_in_manifest = 
[2025-07-28T17:47:44.771953 45836] Config: (default) ignore_options = 
[2025-07-28T17:47:44.771953 45836] Config: (default) inode_cache = false
[2025-07-28T17:47:44.771953 45836] Config: (default) keep_comments_cpp = false
[2025-07-28T17:47:44.771953 45836] Config: (environment) log_file = E:\MONEYC~1\dist\SHIYON~1.ONE\ccache-19176.txt
[2025-07-28T17:47:44.771953 45836] Config: (default) max_files = 0
[2025-07-28T17:47:44.771953 45836] Config: (default) max_size = 5.0 GiB
[2025-07-28T17:47:44.771953 45836] Config: (default) msvc_dep_prefix = Note: including file:
[2025-07-28T17:47:44.771953 45836] Config: (default) namespace = 
[2025-07-28T17:47:44.771953 45836] Config: (default) path = 
[2025-07-28T17:47:44.771953 45836] Config: (default) pch_external_checksum = false
[2025-07-28T17:47:44.771953 45836] Config: (default) prefix_command = 
[2025-07-28T17:47:44.771953 45836] Config: (default) prefix_command_cpp = 
[2025-07-28T17:47:44.771953 45836] Config: (default) read_only = false
[2025-07-28T17:47:44.771953 45836] Config: (default) read_only_direct = false
[2025-07-28T17:47:44.771953 45836] Config: (default) recache = false
[2025-07-28T17:47:44.771953 45836] Config: (default) remote_only = false
[2025-07-28T17:47:44.771953 45836] Config: (default) remote_storage = 
[2025-07-28T17:47:44.771953 45836] Config: (default) reshare = false
[2025-07-28T17:47:44.771953 45836] Config: (default) run_second_cpp = true
[2025-07-28T17:47:44.771953 45836] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-07-28T17:47:44.771953 45836] Config: (default) stats = true
[2025-07-28T17:47:44.771953 45836] Config: (default) stats_log = 
[2025-07-28T17:47:44.771953 45836] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-07-28T17:47:44.771953 45836] Config: (default) umask = 
[2025-07-28T17:47:44.772017 45836] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=20 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\PYTHON~2\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-07-28T17:47:44.774283 45836] Hostname: DESKTOP-419NV80
[2025-07-28T17:47:44.774294 45836] Working directory: E:\MONEYC~1\dist\SHIYON~1.ONE
[2025-07-28T17:47:44.774310 45836] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-07-28T17:47:44.774313 45836] Compiler type: gcc
[2025-07-28T17:47:44.774477 45836] Detected input file: static_src\OnefileBootstrap.c
[2025-07-28T17:47:44.774560 45836] Source file: static_src\OnefileBootstrap.c
[2025-07-28T17:47:44.774563 45836] Object file: static_src\OnefileBootstrap.o
[2025-07-28T17:47:44.774658 45836] Trying direct lookup
[2025-07-28T17:47:44.774799 45836] Manifest key: a01e63k5188lku8nu8eisg3jjc5evnlku
[2025-07-28T17:47:44.774857 45836] Retrieved a01e63k5188lku8nu8eisg3jjc5evnlku from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/0/1e63k5188lku8nu8eisg3jjc5evnlkuM)
[2025-07-28T17:47:44.793445 45836] Got result key from manifest
[2025-07-28T17:47:44.793468 45836] Result key: 6e9eca5idcr9bc43kro8n39fqormr5gee
[2025-07-28T17:47:44.793665 45836] Retrieved 6e9eca5idcr9bc43kro8n39fqormr5gee from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/e/9eca5idcr9bc43kro8n39fqormr5geeR)
[2025-07-28T17:47:44.794084 45836] Reading embedded entry #0 .o (613169 bytes)
[2025-07-28T17:47:44.794097 45836] Writing to static_src\OnefileBootstrap.o
[2025-07-28T17:47:44.794408 45836] Succeeded getting cached result
[2025-07-28T17:47:44.794511 45836] Result: direct_cache_hit
[2025-07-28T17:47:44.794515 45836] Result: local_storage_hit
[2025-07-28T17:47:44.794518 45836] Result: local_storage_read_hit
[2025-07-28T17:47:44.794521 45836] Result: local_storage_read_hit
[2025-07-28T17:47:44.794536 45836] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/c/stats.lock
[2025-07-28T17:47:44.794872 45836] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/c/stats.lock
[2025-07-28T17:47:44.795163 45836] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/c/stats.lock
[2025-07-28T17:47:44.795221 45836] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/0/c/stats.lock
