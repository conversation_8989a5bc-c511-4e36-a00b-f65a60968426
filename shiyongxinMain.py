#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
释永信-业内独创视频号爆流
基于Codebase_shiyongxin.py的多线程视频处理软件
佛教风格UI设计
"""

import os
import sys
import cv2
import threading
import tempfile
import hashlib
import subprocess
import requests
import json
import logging
import traceback
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QProgressBar,
                             QFileDialog, QSpinBox, QTextEdit, QGroupBox,
                             QMessageBox, QFrame, QLineEdit, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSettings, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt5.QtGui import QFont, QPalette, QColor, QIcon

# 导入释永信处理器
from Codebase_shiyongxin import WolverineProcessor

# 登录配置
SOFTWARE_ID = "H012EHIYPJOXZOTA"  # 软件标识
VERSION = "1.0"                   # 版本号
API_NEW_LOGIN = "http://47.122.30.31/api/keysystem/verify_key.php"  # 新登录通道
API_NEW_UNBIND = "http://47.122.30.31/api/keysystem/unbind.php"     # 新解绑通道
API_NEW_ANNOUNCEMENT = "http://47.122.30.31/api/keysystem/announcement.php"  # 新公告通道

# 配置日志
temp_dir = tempfile.gettempdir()
login_log_path = os.path.join(temp_dir, "ShiYongXin_login.log")
main_log_path = os.path.join(temp_dir, "ShiYongXin_main.log")

# 配置详细的日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(login_log_path, encoding='utf-8'),
        logging.FileHandler(main_log_path, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 添加全局异常处理
def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    error_msg = f"未捕获的异常: {exc_type.__name__}: {exc_value}"
    logging.critical(error_msg, exc_info=(exc_type, exc_value, exc_traceback))

    # 在打包环境中也显示错误对话框
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        app = QApplication.instance()
        if app:
            QMessageBox.critical(None, '严重错误', f'{error_msg}\n\n详细信息请查看日志文件:\n{main_log_path}')
    except:
        pass

sys.excepthook = handle_exception

# 新通道错误码映射
NEW_ERROR_MESSAGES = {
    "-1001": "参数错误",
    "-1002": "卡密已禁用",
    "-1003": "卡密已过期",
    "-1004": "卡密不存在",
    "-1005": "软件不存在或已禁用",
    "-1006": "版本号无效",
    "-1007": "API密钥无效",
    "-1008": "IP被禁止",
    "-1009": "需要更新版本",
    "-1010": "请求过于频繁",
    "-1011": "缺少机器码",
    "-1012": "机器码不匹配",
    "-1097": "黑名单检查失败",
    "-1098": "数据库查询错误",
    "-1099": "系统错误",
    "-1101": "解绑失败，已达到最大解绑次数",
    "-1102": "不允许解绑",
    "-1201": "积分不足",
    "-1301": "功能未授权"
}

def get_machine_code():
    """获取机器码 - 使用多种方法确保成功"""

    def get_machine_code_wmic():
        """方法1：使用WMIC获取CPU ProcessorId"""
        try:
            logging.debug("尝试使用WMIC获取机器码")
            output = subprocess.check_output("wmic cpu get ProcessorId", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "ProcessorId":
                    logging.debug(f"WMIC获取到ProcessorId: {line[:8]}...")
                    return line
            raise Exception("WMIC输出中未找到有效的ProcessorId")
        except Exception as e:
            logging.debug(f"WMIC方法失败: {str(e)}")
            raise

    def get_machine_code_wmic_format():
        """方法2：使用WMIC格式化输出"""
        try:
            logging.debug("尝试使用WMIC格式化方法获取机器码")
            output = subprocess.check_output(
                ["wmic", "cpu", "get", "ProcessorId", "/format:list"],
                shell=False,
                universal_newlines=True,
                timeout=10
            )
            for line in output.strip().split('\n'):
                if line.startswith('ProcessorId=') and len(line) > 12:
                    processor_id = line.split('=', 1)[1].strip()
                    if processor_id:
                        logging.debug(f"WMIC格式化获取到ProcessorId: {processor_id[:8]}...")
                        return processor_id
            raise Exception("WMIC格式化输出中未找到有效的ProcessorId")
        except Exception as e:
            logging.debug(f"WMIC格式化方法失败: {str(e)}")
            raise

    def get_machine_code_powershell():
        """方法3：使用PowerShell获取CPU信息"""
        try:
            logging.debug("尝试使用PowerShell获取机器码")
            ps_command = "Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty ProcessorId"
            output = subprocess.check_output(
                ["powershell", "-Command", ps_command],
                shell=False,
                universal_newlines=True,
                timeout=10
            )
            processor_id = output.strip()
            if processor_id:
                logging.debug(f"PowerShell获取到ProcessorId: {processor_id[:8]}...")
                return processor_id
            raise Exception("PowerShell输出为空")
        except Exception as e:
            logging.debug(f"PowerShell方法失败: {str(e)}")
            raise

    def get_machine_code_motherboard():
        """方法4：使用主板序列号作为备用"""
        try:
            logging.debug("尝试使用主板序列号获取机器码")
            output = subprocess.check_output("wmic baseboard get SerialNumber", shell=True, universal_newlines=True, timeout=10)
            lines = output.strip().split('\n')
            for i in range(1, len(lines)):
                line = lines[i].strip()
                if line and line != "SerialNumber" and line != "To be filled by O.E.M." and len(line) > 3:
                    logging.debug(f"主板序列号获取成功: {line[:8]}...")
                    return f"MB_{line}"
            raise Exception("未找到有效的主板序列号")
        except Exception as e:
            logging.debug(f"主板序列号方法失败: {str(e)}")
            raise

    # 按优先级尝试不同方法
    methods = [
        ("WMIC", get_machine_code_wmic),
        ("WMIC格式化", get_machine_code_wmic_format),
        ("PowerShell", get_machine_code_powershell),
        ("主板序列号", get_machine_code_motherboard)
    ]

    last_error = None
    for method_name, method_func in methods:
        try:
            logging.debug(f"尝试使用{method_name}方法获取机器码")
            processor_id = method_func()

            # 使用SHA256加密
            hash_object = hashlib.sha256(processor_id.encode('utf-8'))
            machine_code = hash_object.hexdigest()[:32]
            logging.debug(f"机器码获取成功({method_name}): {machine_code[:8]}...")
            return machine_code

        except Exception as e:
            last_error = e
            logging.debug(f"{method_name}方法失败，尝试下一种方法")
            continue

    # 所有方法都失败了
    error_msg = (
        "无法获取机器码，所有方法都失败了。\n\n"
        "可能的解决方案：\n"
        "1. 以管理员身份运行程序\n"
        "2. 检查Windows Management Instrumentation服务是否正常运行\n"
        "3. 重启计算机后重试\n\n"
        f"技术详情: {str(last_error)}"
    )
    logging.error(error_msg)
    raise Exception(error_msg)

class NetworkThread(QThread):
    """网络请求线程"""
    finished = pyqtSignal(object)  # 完成信号
    error = pyqtSignal(str)        # 错误信号

    def __init__(self, url, data, headers=None, method="POST"):
        super().__init__()
        self.url = url
        self.data = data
        self.headers = headers or {}
        self.method = method.upper()
        self._is_running = True

    def run(self):
        try:
            if not self._is_running:
                return

            logging.debug(f"发送{self.method}请求到 {self.url}")

            session = requests.Session()
            session.mount('http://', requests.adapters.HTTPAdapter(max_retries=3))
            session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))

            is_json = False
            if self.headers and 'Content-Type' in self.headers and 'application/json' in self.headers['Content-Type']:
                is_json = True
                logging.debug(f"使用JSON格式发送数据: {self.data}")

            if self.method == "GET":
                response = session.get(
                    self.url,
                    params=self.data,
                    headers=self.headers,
                    timeout=(5, 10),
                    verify=False
                )
            else:
                response = session.post(
                    self.url,
                    json=self.data if is_json else None,
                    data=None if is_json else self.data,
                    headers=self.headers,
                    timeout=(5, 10),
                    verify=False
                )

            if not self._is_running:
                return

            response.raise_for_status()
            logging.debug(f"请求成功: {response.text[:100]}")
            self.finished.emit(response)

        except requests.exceptions.Timeout:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"请求超时: {error_msg}")
            self.error.emit(error_msg)
        except requests.exceptions.ConnectionError:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"网络连接失败: {error_msg}")
            self.error.emit(error_msg)
        except Exception as e:
            error_msg = "验证失败：请尝试重新启动或联系客服处理。"
            logging.error(f"网络请求失败: {str(e)}")
            self.error.emit(error_msg)

    def stop(self):
        """停止线程"""
        self._is_running = False
        self.wait()

class BuddhistTitleLabel(QLabel):
    """佛教风格的动画标题标签"""

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self._animation_value = 0.0
        self.setup_animation()

    def setup_animation(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self, b"animationValue")
        self.animation.setDuration(4000)  # 4秒完成一个周期
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.InOutSine)
        self.animation.setLoopCount(-1)  # 无限循环
        self.animation.valueChanged.connect(self.update_gradient)
        self.animation.start()

    @pyqtProperty(float)
    def animationValue(self):
        return self._animation_value

    @animationValue.setter
    def animationValue(self, value):
        self._animation_value = value
        self.update_gradient()

    def update_gradient(self):
        """更新佛教风格渐变样式"""
        progress = self._animation_value

        # 佛教紫金色到赤金色的渐变（更独特的佛教色彩）
        purple_gold_r = int(138 + progress * 80)   # 138 -> 218 (紫金色)
        purple_gold_g = int(43 + progress * 122)   # 43 -> 165
        purple_gold_b = int(226 - progress * 150)  # 226 -> 76

        red_gold_r = int(255 - progress * 37)      # 255 -> 218 (赤金色)
        red_gold_g = int(215 - progress * 50)      # 215 -> 165
        red_gold_b = int(0 + progress * 32)        # 0 -> 32

        # 计算光晕效果
        glow_intensity = int(20 + progress * 15)   # 20 -> 35

        gradient_style = f"""
            QLabel {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgb({purple_gold_r}, {purple_gold_g}, {purple_gold_b}),
                    stop:0.3 rgb({int((purple_gold_r + red_gold_r) / 2)}, {int((purple_gold_g + red_gold_g) / 2)}, {int((purple_gold_b + red_gold_b) / 2)}),
                    stop:0.7 rgb({red_gold_r}, {red_gold_g}, {red_gold_b}),
                    stop:1 rgb({purple_gold_r}, {purple_gold_g}, {purple_gold_b}));
                color: #FFFAF0;
                font-size: 24px;
                font-weight: bold;
                border-radius: 15px;
                padding: 18px;
                border: 3px solid rgba(218, 165, 32, 0.8);
                box-shadow: 0 0 {glow_intensity}px rgba(218, 165, 32, 0.6);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            }}
        """
        self.setStyleSheet(gradient_style)

class ShiYongXinLoginWindow(QWidget):
    """释永信登录窗口 - 佛教风格"""
    login_success = pyqtSignal(str, str, bool)  # 到期时间, 卡密, 静音状态

    def __init__(self):
        super().__init__()
        logging.debug("初始化释永信登录窗口")
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        self.initUI()
        self.load_saved_credentials()

        # 延迟加载公告
        QTimer.singleShot(100, self.load_announcement)

        # 初始化线程变量
        self.announcement_thread = None
        self.login_thread = None
        self.unbind_thread = None

    def initUI(self):
        """初始化佛教风格UI"""
        self.setWindowTitle('释永信-业内独创视频号爆流-登录')
        self.setFixedSize(500, 850)
        self.setWindowFlags(Qt.WindowCloseButtonHint)

        # 独特的佛教风格样式
        self.setStyleSheet("""
            QWidget {
                background: qradialgradient(cx:0.5, cy:0.3, radius:1.2,
                    stop:0 #FFF8DC, stop:0.6 #F0E68C, stop:1 #DEB887);
                color: #654321;
                font-family: "Microsoft YaHei", "KaiTi", "SimSun";
            }
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFFEF7, stop:1 #FFF8DC);
                color: #654321;
                border: 3px solid transparent;
                border-image: linear-gradient(45deg, #DAA520, #CD853F, #B8860B) 1;
                border-radius: 12px;
                padding: 15px;
                font-size: 15px;
                min-height: 30px;
                selection-background-color: #DAA520;
            }
            QLineEdit:focus {
                border-image: linear-gradient(45deg, #FFD700, #FFA500, #DAA520) 1;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFFFF0, stop:1 #FFFACD);
                box-shadow: 0 0 15px rgba(218, 165, 32, 0.5);
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:0.5 #DAA520, stop:1 #B8860B);
                color: #FFFAF0;
                border: 2px solid #8B7355;
                border-radius: 12px;
                padding: 15px 30px;
                font-size: 16px;
                font-weight: bold;
                min-height: 40px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFF00, stop:0.5 #FFD700, stop:1 #DAA520);
                border: 2px solid #654321;
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #B8860B, stop:0.5 #8B7355, stop:1 #654321);
                transform: translateY(1px);
            }
            QCheckBox {
                color: #654321;
                font-size: 15px;
                font-weight: bold;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 24px;
                height: 24px;
            }
            QCheckBox::indicator:unchecked {
                border: 3px solid #CD853F;
                border-radius: 6px;
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.8,
                    stop:0 #FFFAF0, stop:1 #F5DEB3);
            }
            QCheckBox::indicator:checked {
                border: 3px solid #DAA520;
                border-radius: 6px;
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.8,
                    stop:0 #FFD700, stop:1 #DAA520);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE1IDVMNyAxM0wzIDkiIHN0cm9rZT0iI0ZGRkFGMCIgc3Ryb2tlLXdpZHRoPSIzIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            QLabel {
                color: #654321;
                font-size: 16px;
                font-weight: bold;
            }
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFFEF7, stop:1 #FFF8DC);
                color: #654321;
                border: 3px solid transparent;
                border-image: linear-gradient(135deg, #DAA520, #CD853F) 1;
                border-radius: 12px;
                padding: 15px;
                font-size: 15px;
                selection-background-color: #DAA520;
            }
            QTextEdit:focus {
                border-image: linear-gradient(135deg, #FFD700, #DAA520) 1;
            }
        """)

        # 创建主布局
        layout = QVBoxLayout()
        layout.setContentsMargins(50, 50, 50, 50)
        layout.setSpacing(25)

        # 佛教风格标题
        self.title_label = BuddhistTitleLabel('🙏 释永信-业内独创视频号爆流 🙏')
        self.title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)

        # 佛教祝福语
        blessing_label = QLabel('南无阿弥陀佛 🙏 愿您视频号爆流如意')
        blessing_label.setStyleSheet('font-size: 18px; color: #DAA520; font-weight: bold;')
        blessing_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(blessing_label)

        # 公告区域
        notice_label = QLabel('📿 佛门公告 📿')
        notice_label.setStyleSheet('font-size: 16px; color: #8B4513; font-weight: bold;')
        notice_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(notice_label)

        # 公告文本框
        self.notice_text = QTextEdit()
        self.notice_text.setReadOnly(True)
        self.notice_text.setFixedHeight(240)
        layout.addWidget(self.notice_text)

        # 卡密输入框
        key_label = QLabel('🔑 请输入您的法门密钥：')
        key_label.setStyleSheet('font-size: 16px; color: #8B4513; font-weight: bold;')
        layout.addWidget(key_label)

        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText('请输入您的专属密钥以开启法门')
        self.key_input.setFont(QFont('Arial', 12))
        layout.addWidget(self.key_input)

        # 选项区域
        checkbox_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox('🧘 记住密钥')
        self.remember_checkbox.setFont(QFont('Microsoft YaHei', 12))
        self.mute_checkbox = QCheckBox('回归世俗（兼容性开启）')
        self.mute_checkbox.setFont(QFont('Microsoft YaHei', 12))
        self.mute_checkbox.setChecked(False)
        checkbox_layout.addWidget(self.remember_checkbox)
        checkbox_layout.addWidget(self.mute_checkbox)
        checkbox_layout.addStretch()
        layout.addLayout(checkbox_layout)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 登录按钮
        self.login_button = QPushButton('🚪 开启法门')
        self.login_button.setFont(QFont('Microsoft YaHei', 14, QFont.Bold))
        self.login_button.clicked.connect(self.login)
        button_layout.addWidget(self.login_button)

        # 解绑按钮
        self.unbind_button = QPushButton('🔓 解除绑定')
        self.unbind_button.setFont(QFont('Microsoft YaHei', 14, QFont.Bold))
        self.unbind_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #CD853F, stop:1 #A0522D);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #DAA520, stop:1 #CD853F);
            }
        """)
        self.unbind_button.clicked.connect(self.unbind)
        button_layout.addWidget(self.unbind_button)

        layout.addLayout(button_layout)

        # 添加弹性空间
        layout.addStretch()

        # 底部版权信息
        copyright_label = QLabel('🏛️ 释永信™ 佛门科技 - 视频号技术加持 🏛️')
        copyright_label.setStyleSheet('color: #8B4513; font-size: 14px; font-weight: bold;')
        copyright_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(copyright_label)

        self.setLayout(layout)
        logging.debug("佛教风格UI初始化完成")

    def load_saved_credentials(self):
        """加载保存的卡密"""
        try:
            settings = QSettings('ShiYongXin', 'VideoFlow')
            saved_key = settings.value('saved_key', '')
            remember = settings.value('remember_key', False, type=bool)
            mute = settings.value('mute', False, type=bool)

            if remember and saved_key:
                self.key_input.setText(saved_key)
                self.remember_checkbox.setChecked(True)
                self.mute_checkbox.setChecked(mute)
                logging.debug("已加载保存的密钥")
        except Exception as e:
            logging.error(f"加载保存的密钥失败: {str(e)}")

    def save_credentials(self):
        """保存卡密"""
        try:
            settings = QSettings('ShiYongXin', 'VideoFlow')
            if self.remember_checkbox.isChecked():
                settings.setValue('saved_key', self.key_input.text())
                settings.setValue('remember_key', True)
                settings.setValue('mute', self.mute_checkbox.isChecked())
                logging.debug("已保存密钥")
            else:
                settings.remove('saved_key')
                settings.setValue('remember_key', False)
                settings.setValue('mute', self.mute_checkbox.isChecked())
                logging.debug("已清除保存的密钥")
        except Exception as e:
            logging.error(f"保存密钥失败: {str(e)}")

    def load_announcement(self):
        """加载公告"""
        try:
            self.notice_text.setText("正在加载佛门公告...\n南无阿弥陀佛 🙏")

            params = {'software_identifier': SOFTWARE_ID}

            self.announcement_thread = NetworkThread(
                API_NEW_ANNOUNCEMENT,
                params,
                method="GET"
            )
            self.announcement_thread.finished.connect(self.handle_announcement)
            self.announcement_thread.error.connect(self.handle_announcement_error)
            self.announcement_thread.start()
            logging.debug("开始加载佛门公告")
        except Exception as e:
            logging.error(f"加载公告失败: {str(e)}")
            self.notice_text.setText("公告加载失败，请稍后重试\n南无阿弥陀佛 🙏")

    def handle_announcement(self, response):
        """处理公告响应"""
        try:
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                logging.error(f"获取公告失败，错误码: {response_text}")
                self.notice_text.setText("公告加载失败，请稍后重试\n南无阿弥陀佛 🙏")
                return

            try:
                response_data = response.json()
                announcement = response_data.get("announcement", "")
                if announcement:
                    self.notice_text.setText(f"📿 {announcement}\n\n南无阿弥陀佛 🙏")
                    logging.debug("佛门公告加载成功")
                else:
                    self.notice_text.setText("暂无佛门公告\n南无阿弥陀佛 🙏")
            except:
                announcement = response.text
                self.notice_text.setText(f"📿 {announcement}\n\n南无阿弥陀佛 🙏")
                logging.debug("佛门公告加载成功")
        except Exception as e:
            logging.error(f"处理公告失败: {str(e)}")
            self.handle_announcement_error(f"处理公告失败: {str(e)}")

    def handle_announcement_error(self, error_msg):
        """处理公告加载错误"""
        try:
            logging.error(f"公告加载错误: {error_msg}")
            self.notice_text.setText("公告加载失败，请稍后重试\n南无阿弥陀佛 🙏")
        except Exception as e:
            logging.error(f"处理公告错误失败: {str(e)}")

    def login(self):
        """处理登录逻辑"""
        try:
            key = self.key_input.text().strip()

            if not key:
                QMessageBox.warning(self, '提示', '请输入法门密钥')
                return

            # 禁用登录按钮
            self.login_button.setEnabled(False)
            self.login_button.setText('🙏 正在开启法门...')

            try:
                machine_code = get_machine_code()
            except Exception as e:
                self.handle_error(str(e))
                self.reset_login_button()
                return

            # 根据密钥长度选择通道
            key_length = len(key)

            if 17 <= key_length <= 32:
                # 使用新通道登录
                logging.debug("使用新通道登录")
                data = {
                    "software_identifier": SOFTWARE_ID,
                    "key_code": key,
                    "machine_code": machine_code
                }
                headers = {"Content-Type": "application/json;charset=UTF-8"}
                self.login_thread = NetworkThread(API_NEW_LOGIN, data, headers)
                self.login_thread.finished.connect(lambda response: self.handle_login_response(response, key))
            else:
                QMessageBox.warning(self, '提示', '密钥格式不正确，请输入有效的法门密钥')
                self.reset_login_button()
                return

            self.login_thread.error.connect(self.handle_login_error)
            self.login_thread.start()
            logging.debug("开始登录请求")

        except Exception as e:
            logging.error(f"登录处理失败: {str(e)}")
            self.handle_error(f"登录处理失败: {str(e)}")
            self.reset_login_button()

    def reset_login_button(self):
        """重置登录按钮状态"""
        try:
            self.login_button.setEnabled(True)
            self.login_button.setText('🚪 开启法门')
        except Exception as e:
            logging.error(f"重置登录按钮失败: {str(e)}")

    def handle_login_error(self, error_msg):
        """处理登录错误"""
        try:
            logging.error(f"登录错误: {error_msg}")
            self.handle_error(error_msg)
            self.reset_login_button()
        except Exception as e:
            logging.error(f"处理登录错误失败: {str(e)}")
            self.reset_login_button()

    def handle_login_response(self, response, key):
        """处理登录响应"""
        try:
            # 检查响应是否为错误码
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                error_code = response_text
                if error_code in NEW_ERROR_MESSAGES:
                    QMessageBox.critical(self, '法门未开', f'🚫 {NEW_ERROR_MESSAGES[error_code]}')
                else:
                    QMessageBox.critical(self, '法门未开', f'🚫 验证失败：错误码 {error_code}')
                self.reset_login_button()
                return

            # 解析JSON响应
            try:
                response_data = response.json()
                if "token" in response_data and "expires" in response_data:
                    # 登录成功
                    expiry_time_str = response_data["expires"]

                    # 保存凭据
                    self.save_credentials()

                    # 显示成功消息
                    QMessageBox.information(self, '法门已开', f'🙏 南无阿弥陀佛！法门已为您开启\n\n到期时间: {expiry_time_str}')

                    # 发射登录成功信号
                    self.login_success.emit(expiry_time_str, key, self.mute_checkbox.isChecked())

                    # 隐藏登录窗口
                    self.hide()
                    logging.debug("登录成功")
                    return
                else:
                    error_code = str(response_data.get("error", ""))
                    if error_code in NEW_ERROR_MESSAGES:
                        QMessageBox.critical(self, '法门未开', f'🚫 {NEW_ERROR_MESSAGES[error_code]}')
                    else:
                        QMessageBox.critical(self, '法门未开', '🚫 验证失败：请尝试重新启动或联系客服处理。')
                    self.reset_login_button()
                    return
            except Exception as e:
                logging.error(f"解析登录响应失败: {str(e)}")
                QMessageBox.critical(self, '法门未开', '🚫 验证失败：请尝试重新启动或联系客服处理。')
                self.reset_login_button()
                return

        except Exception as e:
            logging.error(f"处理登录响应失败: {str(e)}")
            self.handle_error(f"处理登录响应失败: {str(e)}")
            self.reset_login_button()

    def unbind(self):
        """处理解绑逻辑"""
        try:
            key = self.key_input.text().strip()
            if not key:
                QMessageBox.warning(self, '提示', '请输入法门密钥以进行解绑')
                return

            key_length = len(key)

            if 17 <= key_length <= 32:
                reply = QMessageBox.question(self, '确认解绑',
                                           '🔓 确定要解除当前设备的绑定吗？\n解绑后需要重新输入密钥。\n\n南无阿弥陀佛 🙏',
                                           QMessageBox.Yes | QMessageBox.No,
                                           QMessageBox.No)

                if reply == QMessageBox.Yes:
                    self.unbind_button.setEnabled(False)
                    self.unbind_button.setText('🙏 正在解绑...')

                    try:
                        machine_code = get_machine_code()
                    except Exception as e:
                        self.handle_error(str(e))
                        self.reset_unbind_button()
                        return

                    data = {
                        "key_code": key,
                        "software_identifier": SOFTWARE_ID,
                        "machine_code": machine_code
                    }

                    headers = {"Content-Type": "application/json;charset=UTF-8"}

                    self.unbind_thread = NetworkThread(API_NEW_UNBIND, data, headers)
                    self.unbind_thread.finished.connect(self.handle_unbind_response)
                    self.unbind_thread.error.connect(self.handle_unbind_error)
                    self.unbind_thread.start()
                    logging.debug("开始解绑请求")
            else:
                QMessageBox.warning(self, '提示', '密钥格式不正确，请输入有效的法门密钥')
                return

        except Exception as e:
            logging.error(f"解绑处理失败: {str(e)}")
            self.handle_error(f"解绑处理失败: {str(e)}")
            self.reset_unbind_button()

    def reset_unbind_button(self):
        """重置解绑按钮状态"""
        try:
            self.unbind_button.setEnabled(True)
            self.unbind_button.setText('🔓 解除绑定')
        except Exception as e:
            logging.error(f"重置解绑按钮失败: {str(e)}")

    def handle_unbind_error(self, error_msg):
        """处理解绑错误"""
        try:
            logging.error(f"解绑错误: {error_msg}")
            self.handle_error(error_msg)
            self.reset_unbind_button()
        except Exception as e:
            logging.error(f"处理解绑错误失败: {str(e)}")
            self.reset_unbind_button()

    def handle_unbind_response(self, response):
        """处理解绑响应"""
        try:
            response_text = response.text.strip()
            if response_text.startswith("-") and response_text[1:].isdigit():
                error_code = response_text
                if error_code in NEW_ERROR_MESSAGES:
                    QMessageBox.critical(self, '解绑失败', f'🚫 {NEW_ERROR_MESSAGES[error_code]}')
                else:
                    QMessageBox.critical(self, '解绑失败', f'🚫 解绑失败：错误码 {error_code}')
                self.reset_unbind_button()
                return

            try:
                response_data = response.json()

                if response_data.get("success") == True:
                    remaining_unbinds = response_data.get("remaining_unbinds", 0)

                    # 清除保存的密钥
                    settings = QSettings('ShiYongXin', 'VideoFlow')
                    settings.remove('saved_key')
                    settings.setValue('remember_key', False)

                    # 清空输入框
                    self.key_input.clear()
                    self.remember_checkbox.setChecked(False)

                    QMessageBox.information(self, '解绑成功', f'🙏 设备已解绑，请在新设备重新输入密钥。\n剩余解绑次数：{remaining_unbinds}\n\n南无阿弥陀佛')
                    logging.debug(f"解绑成功，剩余次数：{remaining_unbinds}")
                else:
                    error_message = response_data.get("message", "验证失败：请尝试重新启动或联系客服处理。")
                    QMessageBox.critical(self, '解绑失败', f'🚫 {error_message}')
                    logging.warning(f"解绑失败: {error_message}")
            except Exception as e:
                logging.error(f"解析解绑响应失败: {str(e)}")
                QMessageBox.critical(self, '解绑失败', '🚫 验证失败：请尝试重新启动或联系客服处理。')
        except Exception as e:
            logging.error(f"处理解绑响应失败: {str(e)}")
            self.handle_error(f"处理解绑响应失败: {str(e)}")
        finally:
            self.reset_unbind_button()

    def handle_error(self, error_msg):
        """处理一般错误"""
        try:
            logging.error(error_msg)
            if error_msg in NEW_ERROR_MESSAGES:
                QMessageBox.critical(self, '验证失败', f'🚫 {NEW_ERROR_MESSAGES[error_msg]}')
            else:
                QMessageBox.critical(self, '验证失败', '🚫 验证失败：请尝试重新启动或联系客服处理。')
        except Exception as e:
            logging.error(f"处理错误失败: {str(e)}")
            QMessageBox.critical(self, '验证失败', '🚫 验证失败：请尝试重新启动或联系客服处理。')

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            logging.debug("登录窗口关闭")
            # 停止所有网络线程
            for thread in [self.announcement_thread, self.login_thread, self.unbind_thread]:
                if thread and thread.isRunning():
                    thread.stop()
            # 停止标题动画
            if hasattr(self, 'title_label') and hasattr(self.title_label, 'animation'):
                self.title_label.animation.stop()
            event.accept()
        except Exception as e:
            logging.error(f"窗口关闭处理失败: {str(e)}")
            event.accept()

class ShiYongXinMultiProcessor(QThread):
    """释永信多线程处理器"""
    
    progress_updated = pyqtSignal(int, str)
    process_finished = pyqtSignal(bool, str)
    
    def __init__(self, ffmpeg_path, a_video_list, b_video_list, output_dir, thread_count=2):
        super().__init__()
        self.ffmpeg_path = ffmpeg_path
        self.a_video_list = a_video_list
        self.b_video_list = b_video_list
        self.output_dir = output_dir
        self.thread_count = thread_count
        self.is_cancelled = False
        self.longest_video_path = ""
        self.longest_video_duration = 0
        
        # 找到时长最长的A视频用于显示进度
        self.find_longest_video()
        
    def find_longest_video(self):
        """找到时长最长的A视频"""
        max_duration = 0
        longest_path = ""
        
        for video_path in self.a_video_list:
            try:
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS) or 30
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = frame_count / fps
                    
                    if duration > max_duration:
                        max_duration = duration
                        longest_path = video_path
                    
                    cap.release()
            except Exception as e:
                print(f"[释永信] 检测视频时长失败: {video_path} - {e}")
        
        self.longest_video_path = longest_path
        self.longest_video_duration = max_duration
        print(f"[释永信] 最长视频: {os.path.basename(longest_path)} ({max_duration:.1f}秒)")
    
    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        print("[释永信] 多线程处理已取消")
    
    def run(self):
        """多线程处理主流程"""
        try:
            if not self.a_video_list:
                self.process_finished.emit(False, "A视频列表为空")
                return
                
            if not self.b_video_list:
                self.process_finished.emit(False, "B视频列表为空")
                return
            
            total_videos = len(self.a_video_list)
            completed_videos = 0
            failed_videos = 0
            
            # 创建线程池
            with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                # 提交所有任务
                future_to_video = {}
                b_video_index = 0
                
                for i, a_video in enumerate(self.a_video_list):
                    if self.is_cancelled:
                        break
                    
                    # 循环使用B视频
                    b_video = self.b_video_list[b_video_index % len(self.b_video_list)]
                    b_video_index += 1
                    
                    # 提交处理任务
                    future = executor.submit(self.process_single_video, a_video, b_video, i + 1)
                    future_to_video[future] = (a_video, b_video, i + 1)
                
                # 处理完成的任务
                for future in as_completed(future_to_video):
                    if self.is_cancelled:
                        break
                    
                    a_video, b_video, index = future_to_video[future]
                    
                    try:
                        success = future.result()
                        if success:
                            completed_videos += 1
                        else:
                            failed_videos += 1
                    except Exception as e:
                        print(f"[释永信] 处理视频异常: {os.path.basename(a_video)} - {e}")
                        failed_videos += 1
                    
                    # 更新整体进度
                    progress = int((completed_videos + failed_videos) / total_videos * 100)
                    status_msg = f"已完成: {completed_videos}/{total_videos} (失败: {failed_videos})"
                    self.progress_updated.emit(progress, status_msg)
            
            if not self.is_cancelled:
                if failed_videos == 0:
                    self.process_finished.emit(True, f"开光成功！共处理 {completed_videos} 个视频")
                else:
                    self.process_finished.emit(True, f"处理完成：成功 {completed_videos} 个，失败 {failed_videos} 个")
            
        except Exception as e:
            print(f"[释永信] 多线程处理失败: {e}")
            self.process_finished.emit(False, f"处理失败: {str(e)}")
    
    def process_single_video(self, a_video, b_video, index):
        """处理单个视频"""
        try:
            print(f"[释永信] 线程 {threading.current_thread().name} 开始处理第{index}个视频")

            # 生成输出文件名：A视频文件名_开光成功.mp4
            a_name = Path(a_video).stem
            output_filename = f"{a_name}_开光成功.mp4"
            final_output = os.path.join(self.output_dir, output_filename)

            # 创建临时输出目录用于单个处理器
            import tempfile
            temp_output_dir = tempfile.mkdtemp(prefix="shiyongxin_")

            try:
                # 创建单个处理器实例
                processor = WolverineProcessor(
                    self.ffmpeg_path, [a_video], [b_video],
                    temp_output_dir, "开光成功", False
                )

                # 手动设置temp_dir，因为我们不调用run方法
                processor.temp_dir = tempfile.mkdtemp(prefix="wolverine_")

                # 如果是最长视频，连接进度信号
                if a_video == self.longest_video_path:
                    processor.progress_updated.connect(self.progress_updated.emit)

                # 直接调用处理方法
                success = processor.process_video_pair(a_video, b_video, index)

                if success:
                    # 查找生成的文件并移动到最终位置
                    temp_output = os.path.join(temp_output_dir, f"{a_name}_疾风sph.mp4")
                    if os.path.exists(temp_output):
                        import shutil
                        shutil.move(temp_output, final_output)
                        print(f"[释永信] 开光成功: {output_filename}")
                    else:
                        print(f"[释永信] 警告: 未找到临时输出文件 {temp_output}")
                        success = False

                # 清理处理器的临时目录
                if processor.temp_dir and os.path.exists(processor.temp_dir):
                    try:
                        import shutil
                        shutil.rmtree(processor.temp_dir)
                    except Exception as e:
                        print(f"[释永信] 清理处理器临时目录失败: {e}")

                return success

            finally:
                # 清理临时目录
                try:
                    import shutil
                    shutil.rmtree(temp_output_dir)
                except Exception as e:
                    print(f"[释永信] 清理临时目录失败: {e}")

        except Exception as e:
            print(f"[释永信] 处理单个视频失败: {os.path.basename(a_video)} - {e}")
            return False


class ShiYongXinMainWindow(QMainWindow):
    """释永信-业内独创视频号爆流 主窗口"""

    def __init__(self):
        try:
            super().__init__()
            logging.debug("开始初始化主窗口")

            # 基本属性初始化
            self.ffmpeg_path = self.find_ffmpeg()
            self.a_video_folder = ""
            self.b_video_folder = ""
            self.a_video_list = []
            self.b_video_list = []
            self.output_dir = ""
            self.is_processing = False
            self.worker = None

            # 到期时间相关属性
            self.expiry_time = None
            self.card_key = ""

            # 设置到期检查定时器
            try:
                self.expiry_timer = QTimer()
                self.expiry_timer.timeout.connect(self.check_expiry)
                self.expiry_timer.start(10000)  # 每10秒检查一次
                logging.debug("到期检查定时器启动成功")
            except Exception as e:
                logging.error(f"设置到期检查定时器失败: {e}")
                self.expiry_timer = None

            # 初始化UI
            try:
                self.setup_ui()
                logging.debug("UI设置完成")
            except Exception as e:
                logging.error(f"UI设置失败: {e}")
                raise

            try:
                self.apply_buddhist_style()
                logging.debug("样式应用完成")
            except Exception as e:
                logging.error(f"样式应用失败: {e}")

            try:
                self.update_window_title()
                logging.debug("窗口标题更新完成")
            except Exception as e:
                logging.error(f"窗口标题更新失败: {e}")

            logging.debug("主窗口初始化完成")

        except Exception as e:
            logging.critical(f"主窗口初始化失败: {e}", exc_info=True)
            raise
    
    def find_ffmpeg(self):
        """查找FFmpeg路径"""
        # 优先查找bin目录下的ffmpeg
        bin_ffmpeg = os.path.join(os.path.dirname(__file__), "bin", "ffmpeg.exe")
        if os.path.exists(bin_ffmpeg):
            return bin_ffmpeg
        
        # 查找当前目录
        current_ffmpeg = os.path.join(os.path.dirname(__file__), "ffmpeg.exe")
        if os.path.exists(current_ffmpeg):
            return current_ffmpeg
        
        return "ffmpeg"  # 系统PATH中的ffmpeg
    
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("释永信-业内独创视频号爆流 | 南无阿弥陀佛")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题区域
        title_frame = QFrame()
        title_frame.setObjectName("title_frame")
        title_layout = QVBoxLayout(title_frame)
        
        title_label = QLabel("释永信-业内独创视频号爆流")
        title_label.setObjectName("main_title")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("南无阿弥陀佛 | 功德无量 | 视频开光")
        subtitle_label.setObjectName("subtitle")
        subtitle_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(subtitle_label)
        
        main_layout.addWidget(title_frame)
        
        # 文件选择区域
        file_group = QGroupBox("📁 选择视频功德箱")
        file_group.setObjectName("file_group")
        file_layout = QVBoxLayout(file_group)
        
        # A视频功德箱
        a_layout = QHBoxLayout()
        self.a_label = QLabel("A视频功德箱 (主视频): 未选择")
        self.a_label.setObjectName("folder_label")
        a_btn = QPushButton("选择A视频功德箱")
        a_btn.setObjectName("folder_btn")
        a_btn.clicked.connect(self.select_a_video_folder)
        a_layout.addWidget(self.a_label, 1)
        a_layout.addWidget(a_btn)
        file_layout.addLayout(a_layout)
        
        # B视频功德箱
        b_layout = QHBoxLayout()
        self.b_label = QLabel("B视频功德箱 (背景视频): 未选择")
        self.b_label.setObjectName("folder_label")
        b_btn = QPushButton("选择B视频功德箱")
        b_btn.setObjectName("folder_btn")
        b_btn.clicked.connect(self.select_b_video_folder)
        b_layout.addWidget(self.b_label, 1)
        b_layout.addWidget(b_btn)
        file_layout.addLayout(b_layout)
        
        # 输出目录
        output_layout = QHBoxLayout()
        self.output_label = QLabel("输出目录: 默认为A视频所在功德箱")
        self.output_label.setObjectName("folder_label")
        output_btn = QPushButton("选择输出目录")
        output_btn.setObjectName("folder_btn")
        output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.output_label, 1)
        output_layout.addWidget(output_btn)
        file_layout.addLayout(output_layout)
        
        main_layout.addWidget(file_group)
        
        # 处理设置区域
        settings_group = QGroupBox("⚙️ 处理设置")
        settings_group.setObjectName("settings_group")
        settings_layout = QHBoxLayout(settings_group)
        
        thread_label = QLabel("线程数量:")
        thread_label.setObjectName("setting_label")
        self.thread_spinbox = QSpinBox()
        self.thread_spinbox.setRange(1, 8)
        self.thread_spinbox.setValue(2)
        self.thread_spinbox.setObjectName("thread_spinbox")
        
        settings_layout.addWidget(thread_label)
        settings_layout.addWidget(self.thread_spinbox)
        settings_layout.addStretch()
        
        main_layout.addWidget(settings_group)
        
        # 控制按钮区域
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🙏 开始开光")
        self.start_btn.setObjectName("start_btn")
        self.start_btn.clicked.connect(self.start_processing)
        
        self.stop_btn = QPushButton("⏹️ 停止处理")
        self.stop_btn.setObjectName("stop_btn")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        
        main_layout.addLayout(control_layout)
        
        # 进度显示区域
        progress_group = QGroupBox("📊 处理进度")
        progress_group.setObjectName("progress_group")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progress_bar")
        self.progress_bar.setValue(0)
        
        self.status_label = QLabel("准备就绪，等待开光...")
        self.status_label.setObjectName("status_label")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        
        main_layout.addWidget(progress_group)
        
        # 日志显示区域
        log_group = QGroupBox("📝 处理日志")
        log_group.setObjectName("log_group")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setObjectName("log_text")
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group)
        
        # 底部版权信息
        copyright_label = QLabel("© 释永信工作室 | 业内独创技术 | 功德回向众生")
        copyright_label.setObjectName("copyright")
        copyright_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(copyright_label)

    def apply_buddhist_style(self):
        """应用佛教风格样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #8B4513, stop:0.5 #CD853F, stop:1 #DEB887);
            }

            #title_frame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #FFA500);
                border: 3px solid #8B4513;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
            }

            #main_title {
                font-family: "Microsoft YaHei", "SimHei";
                font-size: 28px;
                font-weight: bold;
                color: #8B0000;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }

            #subtitle {
                font-family: "Microsoft YaHei", "KaiTi";
                font-size: 16px;
                color: #8B4513;
                font-style: italic;
                margin-top: 10px;
            }

            QGroupBox {
                font-family: "Microsoft YaHei";
                font-size: 14px;
                font-weight: bold;
                color: #8B4513;
                border: 2px solid #CD853F;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background: rgba(255, 255, 255, 0.1);
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                color: #8B0000;
                font-weight: bold;
            }

            #folder_label {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                color: #2F4F4F;
                background: rgba(255, 255, 255, 0.8);
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #CD853F;
            }

            #folder_btn, #start_btn, #stop_btn {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #DAA520, stop:1 #B8860B);
                border: 2px solid #8B4513;
                border-radius: 8px;
                padding: 10px 20px;
                min-width: 120px;
            }

            #folder_btn:hover, #start_btn:hover, #stop_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFD700, stop:1 #DAA520);
            }

            #start_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6347, stop:1 #DC143C);
                font-size: 16px;
                min-width: 150px;
                min-height: 50px;
            }

            #start_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF7F50, stop:1 #FF6347);
            }

            #stop_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #696969, stop:1 #2F4F4F);
            }

            #setting_label {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                color: #8B4513;
                font-weight: bold;
            }

            #thread_spinbox {
                font-family: "Microsoft YaHei";
                font-size: 12px;
                padding: 5px;
                border: 2px solid #CD853F;
                border-radius: 5px;
                background: white;
                min-width: 60px;
            }

            #progress_bar {
                border: 2px solid #8B4513;
                border-radius: 8px;
                background: #F5F5DC;
                text-align: center;
                font-weight: bold;
                color: #8B4513;
            }

            #progress_bar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FFD700, stop:0.5 #FFA500, stop:1 #FF8C00);
                border-radius: 6px;
            }

            #status_label {
                font-family: "Microsoft YaHei";
                font-size: 14px;
                color: #8B4513;
                font-weight: bold;
                background: rgba(255, 255, 255, 0.8);
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #CD853F;
            }

            #log_text {
                font-family: "Consolas", "Microsoft YaHei";
                font-size: 10px;
                background: #F5F5DC;
                border: 2px solid #CD853F;
                border-radius: 5px;
                color: #2F4F4F;
            }

            #copyright {
                font-family: "KaiTi", "Microsoft YaHei";
                font-size: 12px;
                color: #8B4513;
                font-style: italic;
                background: rgba(255, 255, 255, 0.3);
                padding: 8px;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)

    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def get_video_files_from_folder(self, folder_path):
        """从功德箱获取视频文件列表"""
        if not folder_path or not os.path.exists(folder_path):
            return []

        # 支持的视频格式
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
        video_files = []

        try:
            for file in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in video_extensions:
                        video_files.append(file_path)

            # 按文件名排序
            video_files.sort()
            return video_files

        except Exception as e:
            self.log_message(f"扫描视频文件失败: {e}")
            return []

    def select_a_video_folder(self):
        """选择A视频功德箱"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择A视频功德箱（主视频）")
        if folder_path:
            self.a_video_folder = folder_path
            self.a_video_list = self.get_video_files_from_folder(folder_path)

            folder_name = os.path.basename(folder_path)
            video_count = len(self.a_video_list)

            if video_count > 0:
                self.a_label.setText(f"A视频功德箱: {folder_name} ({video_count} 个视频)")
                self.a_label.setStyleSheet(self.a_label.styleSheet() + "color: #006400;")
                self.log_message(f"选择A视频功德箱: {folder_name}，找到 {video_count} 个视频")

                # 默认设置输出目录为A视频所在功德箱
                if not self.output_dir:
                    self.output_dir = folder_path
                    self.output_label.setText(f"输出目录: {folder_name}")
                    self.output_label.setStyleSheet(self.output_label.styleSheet() + "color: #006400;")
            else:
                self.a_label.setText(f"A视频功德箱: {folder_name} (无视频文件)")
                self.a_label.setStyleSheet(self.a_label.styleSheet() + "color: #DC143C;")
                QMessageBox.warning(self, "警告", "选择的功德箱中没有找到视频文件")
                self.log_message(f"警告: {folder_name} 功德箱中无视频文件")

    def select_b_video_folder(self):
        """选择B视频功德箱"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择B视频功德箱（背景视频）")
        if folder_path:
            self.b_video_folder = folder_path
            self.b_video_list = self.get_video_files_from_folder(folder_path)

            folder_name = os.path.basename(folder_path)
            video_count = len(self.b_video_list)

            if video_count > 0:
                self.b_label.setText(f"B视频功德箱: {folder_name} ({video_count} 个视频)")
                self.b_label.setStyleSheet(self.b_label.styleSheet() + "color: #006400;")
                self.log_message(f"选择B视频功德箱: {folder_name}，找到 {video_count} 个视频")
            else:
                self.b_label.setText(f"B视频功德箱: {folder_name} (无视频文件)")
                self.b_label.setStyleSheet(self.b_label.styleSheet() + "color: #DC143C;")
                QMessageBox.warning(self, "警告", "选择的功德箱中没有找到视频文件")
                self.log_message(f"警告: {folder_name} 功德箱中无视频文件")

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            dir_name = os.path.basename(dir_path)
            self.output_label.setText(f"输出目录: {dir_name}")
            self.output_label.setStyleSheet(self.output_label.styleSheet() + "color: #006400;")
            self.log_message(f"选择输出目录: {dir_name}")

    def start_processing(self):
        """开始处理"""
        # 验证输入
        if not self.a_video_list:
            QMessageBox.warning(self, "警告", "请先选择A视频功德箱")
            return

        if not self.b_video_list:
            QMessageBox.warning(self, "警告", "请先选择B视频功德箱")
            return

        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录")
            return

        if not os.path.exists(self.ffmpeg_path) and self.ffmpeg_path != "ffmpeg":
            QMessageBox.critical(self, "错误", f"找不到FFmpeg: {self.ffmpeg_path}")
            return

        # 设置UI状态
        self.is_processing = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在准备开光...")

        # 获取线程数
        thread_count = self.thread_spinbox.value()

        self.log_message(f"开始开光处理，使用 {thread_count} 个线程")
        self.log_message(f"A视频: {len(self.a_video_list)} 个")
        self.log_message(f"B视频: {len(self.b_video_list)} 个")
        self.log_message(f"输出目录: {self.output_dir}")

        # 创建多线程处理器
        self.worker = ShiYongXinMultiProcessor(
            self.ffmpeg_path, self.a_video_list, self.b_video_list,
            self.output_dir, thread_count
        )

        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.process_finished.connect(self.on_process_finished)

        # 启动处理
        self.worker.start()

    def stop_processing(self):
        """停止处理"""
        if self.worker:
            self.worker.cancel()
            self.worker.wait()

        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("处理已停止")
        self.log_message("用户停止了处理")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        if "开光成功" in message or "处理完成" in message:
            self.log_message(message)

    def on_process_finished(self, success, message):
        """处理完成"""
        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("🎉 " + message)
            self.log_message(f"处理完成: {message}")
            QMessageBox.information(self, "开光成功", f"南无阿弥陀佛！\n\n{message}")
        else:
            self.status_label.setText("❌ " + message)
            self.log_message(f"处理失败: {message}")
            QMessageBox.critical(self, "开光失败", f"处理失败:\n{message}")

    def set_expiry_info(self, expiry_time_str, card_key):
        """设置到期时间信息"""
        try:
            logging.debug(f"开始设置到期信息 - 到期时间字符串: '{expiry_time_str}', 卡密: '{card_key}'")

            self.card_key = card_key if card_key else ""

            if expiry_time_str and expiry_time_str.strip():
                expiry_str_clean = expiry_time_str.strip()
                logging.debug(f"尝试解析到期时间: '{expiry_str_clean}'")

                # 尝试多种时间格式
                time_formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y-%m-%d",
                    "%Y/%m/%d"
                ]

                parsed_time = None
                for fmt in time_formats:
                    try:
                        parsed_time = datetime.strptime(expiry_str_clean, fmt)
                        logging.debug(f"成功使用格式 '{fmt}' 解析时间: {parsed_time}")
                        break
                    except ValueError:
                        continue

                if parsed_time:
                    self.expiry_time = parsed_time
                    current_time = datetime.now()
                    time_diff = self.expiry_time - current_time
                    logging.info(f"设置到期时间成功: {self.expiry_time}, 剩余时间: {time_diff}")
                else:
                    logging.error(f"所有时间格式都无法解析: '{expiry_str_clean}'")
                    self.expiry_time = None
            else:
                self.expiry_time = None
                logging.debug("到期时间字符串为空，设置为永不过期")

            # 更新窗口标题
            try:
                self.update_window_title()
                logging.debug("窗口标题更新成功")
            except Exception as e:
                logging.error(f"更新窗口标题失败: {e}")

        except Exception as e:
            logging.error(f"设置到期信息时发生未预期错误: {e}", exc_info=True)
            self.expiry_time = None
            try:
                self.update_window_title()
            except:
                pass

    def update_window_title(self):
        """更新窗口标题"""
        try:
            base_title = '释永信-业内独创视频号爆流 | 南无阿弥陀佛'
            if self.expiry_time:
                try:
                    expiry_str = self.expiry_time.strftime('%Y-%m-%d %H:%M:%S')
                    title = f'{base_title}（到期时间：{expiry_str}）'
                    logging.debug(f"窗口标题包含到期时间: {expiry_str}")
                except Exception as e:
                    logging.error(f"格式化到期时间失败: {e}")
                    title = base_title
            else:
                title = base_title
                logging.debug("窗口标题不包含到期时间")

            self.setWindowTitle(title)

        except Exception as e:
            logging.error(f"更新窗口标题失败: {e}", exc_info=True)

    def check_expiry(self):
        """检查到期时间"""
        try:
            if not self.expiry_time:
                # 没有设置到期时间，跳过检查
                return

            current_time = datetime.now()
            time_diff = self.expiry_time - current_time

            # 记录剩余时间（每分钟记录一次，避免日志过多）
            if hasattr(self, '_last_log_time'):
                if (current_time - self._last_log_time).total_seconds() >= 60:
                    logging.debug(f"到期检查 - 当前时间: {current_time}, 到期时间: {self.expiry_time}, 剩余: {time_diff}")
                    self._last_log_time = current_time
            else:
                logging.debug(f"首次到期检查 - 当前时间: {current_time}, 到期时间: {self.expiry_time}, 剩余: {time_diff}")
                self._last_log_time = current_time

            if current_time >= self.expiry_time:
                logging.warning("软件已到期，开始执行到期处理流程")

                # 停止定时器
                try:
                    if self.expiry_timer:
                        self.expiry_timer.stop()
                        logging.debug("到期检查定时器已停止")
                except Exception as e:
                    logging.error(f"停止定时器失败: {e}")

                # 禁用所有功能
                try:
                    self.disable_all_functions()
                    logging.debug("所有功能已禁用")
                except Exception as e:
                    logging.error(f"禁用功能失败: {e}")

                # 显示到期提示
                try:
                    QMessageBox.information(self, '提示', '软件使用期限已到，程序将退出。\n南无阿弥陀佛！')
                    logging.info("到期提示对话框已显示")
                except Exception as e:
                    logging.error(f"显示到期提示失败: {e}")

                # 强制关闭软件
                try:
                    logging.info("开始强制关闭软件")
                    self.close()
                    sys.exit(0)
                except Exception as e:
                    logging.critical(f"强制关闭软件失败: {e}", exc_info=True)
                    # 最后的手段
                    os._exit(0)

        except Exception as e:
            logging.error(f"到期检查过程中发生错误: {e}", exc_info=True)

    def disable_all_functions(self):
        """禁用所有功能按钮"""
        try:
            logging.debug("开始禁用所有功能")

            # 禁用所有主要功能按钮
            try:
                if hasattr(self, 'start_btn') and self.start_btn:
                    self.start_btn.setEnabled(False)
                    logging.debug("开始按钮已禁用")
            except Exception as e:
                logging.error(f"禁用开始按钮失败: {e}")

            try:
                if hasattr(self, 'stop_btn') and self.stop_btn:
                    self.stop_btn.setEnabled(False)
                    logging.debug("停止按钮已禁用")
            except Exception as e:
                logging.error(f"禁用停止按钮失败: {e}")

            # 停止正在进行的处理
            try:
                if self.worker and self.worker.isRunning():
                    logging.debug("正在停止工作线程")
                    self.worker.cancel()
                    self.worker.wait()
                    logging.debug("工作线程已停止")
            except Exception as e:
                logging.error(f"停止工作线程失败: {e}")

            # 更新状态
            try:
                if hasattr(self, 'status_label') and self.status_label:
                    self.status_label.setText("软件已到期，功能已禁用")
                    logging.debug("状态标签已更新")
            except Exception as e:
                logging.error(f"更新状态标签失败: {e}")

            logging.info("所有功能禁用完成")

        except Exception as e:
            logging.error(f"禁用功能过程中发生未预期错误: {e}", exc_info=True)

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            logging.debug("开始处理窗口关闭事件")

            # 停止到期检查定时器
            try:
                if hasattr(self, 'expiry_timer') and self.expiry_timer:
                    self.expiry_timer.stop()
                    logging.debug("到期检查定时器已停止")
            except Exception as e:
                logging.error(f"停止到期检查定时器失败: {e}")

            # 停止正在进行的处理
            try:
                if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                    logging.debug("正在停止工作线程")
                    self.worker.cancel()
                    self.worker.wait(5000)  # 等待最多5秒
                    logging.debug("工作线程已停止")
            except Exception as e:
                logging.error(f"停止工作线程失败: {e}")

            logging.info("主窗口关闭处理完成")
            event.accept()

        except Exception as e:
            logging.error(f"窗口关闭处理失败: {e}", exc_info=True)
            event.accept()


def main():
    """主函数"""
    try:
        logging.info("=== 释永信软件启动 ===")

        # 创建应用程序
        try:
            app = QApplication(sys.argv)
            app.setStyle('Fusion')
            logging.debug("QApplication创建成功")
        except Exception as e:
            logging.critical(f"创建QApplication失败: {e}", exc_info=True)
            return 1

        # 设置全局字体
        try:
            font = QFont("Microsoft YaHei", 10)
            app.setFont(font)
            logging.debug("全局字体设置成功")
        except Exception as e:
            logging.error(f"设置全局字体失败: {e}")

        # 创建登录窗口
        try:
            login_window = ShiYongXinLoginWindow()
            logging.debug("登录窗口创建成功")
        except Exception as e:
            logging.critical(f"创建登录窗口失败: {e}", exc_info=True)
            return 1

        # 创建主窗口但不显示
        try:
            main_window = ShiYongXinMainWindow()
            logging.debug("主窗口创建成功")
        except Exception as e:
            logging.critical(f"创建主窗口失败: {e}", exc_info=True)
            return 1

        def on_login_success(expiry_time, card_key, mute_status):
            """登录成功后的处理"""
            try:
                logging.info(f"登录成功回调 - 到期时间: '{expiry_time}', 卡密: '{card_key}', 静音: {mute_status}")

                # 设置主窗口的到期时间信息
                try:
                    main_window.set_expiry_info(expiry_time, card_key)
                    logging.debug("主窗口到期信息设置成功")
                except Exception as e:
                    logging.error(f"设置主窗口到期信息失败: {e}", exc_info=True)

                # 显示主窗口
                try:
                    main_window.show()
                    logging.debug("主窗口显示成功")
                except Exception as e:
                    logging.error(f"显示主窗口失败: {e}", exc_info=True)

                # 关闭登录窗口
                try:
                    login_window.close()
                    logging.debug("登录窗口关闭成功")
                except Exception as e:
                    logging.error(f"关闭登录窗口失败: {e}")

                logging.info("登录成功处理完成")

            except Exception as e:
                logging.error(f"登录成功处理过程中发生错误: {e}", exc_info=True)

        # 连接登录成功信号
        try:
            login_window.login_success.connect(on_login_success)
            logging.debug("登录成功信号连接成功")
        except Exception as e:
            logging.error(f"连接登录成功信号失败: {e}")

        # 显示登录窗口
        try:
            login_window.show()
            logging.debug("登录窗口显示成功")
        except Exception as e:
            logging.critical(f"显示登录窗口失败: {e}", exc_info=True)
            return 1

        logging.info("应用程序启动完成，进入事件循环")
        return app.exec_()

    except Exception as e:
        logging.critical(f"主函数执行失败: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        logging.info(f"程序正常退出，退出码: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        logging.critical(f"程序异常退出: {e}", exc_info=True)
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication.instance()
            if not app:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, '程序异常', f'程序发生严重错误:\n{e}\n\n详细信息请查看日志文件:\n{main_log_path}')
        except:
            pass
        sys.exit(1)
